{"name": "@the-forgebase/database", "version": "0.1.0-alpha.1", "type": "module", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/esm/index.d.ts", "scripts": {"clean": "rm -rf dist", "build:esm": "tsc -p tsconfig.esm.json", "build:cjs": "tsc -p tsconfig.cjs.json", "build": "pnpm clean && pnpm build:esm && pnpm build:cjs", "dev": "tsc -p tsconfig.esm.json --watch", "lint": "tsc --noEmit"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "typesVersions": {"*": {".": ["./dist/esm/index.d.ts"]}}, "files": ["dist", "!**/*.tsbuildinfo", "package.json", "README.md"], "dependencies": {"lodash.flatten": "^4.4.0", "knex": "^3.1.0", "lru-cache": "^11.0.2", "express": "^4.21.2", "crossws": "^0.3.4", "typescript": "5.8.2", "uWebSockets.js": "https://github.com/uNetworking/uWebSockets.js/archive/refs/tags/v20.51.0.tar.gz"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@the-forgebase/typescript-config": "workspace:*", "@types/better-sqlite3": "^7.6.12", "@types/lodash.flatten": "^4.4.9", "@types/lodash.isnil": "^4.0.9", "tslib": "^2.8.1"}}