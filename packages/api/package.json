{"name": "@the-forgebase/api", "version": "0.1.0-alpha.1", "private": false, "type": "module", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/esm/index.d.ts", "scripts": {"clean": "rm -rf dist", "build:esm": "tsc -p tsconfig.esm.json", "build:cjs": "tsc -p tsconfig.cjs.json", "build": "pnpm clean && pnpm build:esm && pnpm build:cjs", "dev": "tsc -p tsconfig.esm.json --watch", "lint": "tsc --noEmit"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./core/nest": {"types": "./dist/esm/core/nest/index.d.ts", "import": "./dist/esm/core/nest/index.js", "require": "./dist/cjs/core/nest/index.js"}, "./core/web": {"types": "./dist/esm/core/web/index.d.ts", "import": "./dist/esm/core/web/index.js", "require": "./dist/cjs/core/web/index.js"}, "./core/express": {"types": "./dist/esm/core/express/index.d.ts", "import": "./dist/esm/core/express/index.js", "require": "./dist/cjs/core/express/index.js"}, "./core/ultimate-express": {"types": "./dist/esm/core/ultimate-express/index.d.ts", "import": "./dist/esm/core/ultimate-express/index.js", "require": "./dist/cjs/core/ultimate-express/index.js"}, "./core": {"types": "./dist/esm/core/index.d.ts", "import": "./dist/esm/core/index.js", "require": "./dist/cjs/core/index.js"}}, "typesVersions": {"*": {".": ["./dist/esm/index.d.ts"], "frameworks": ["./dist/esm/frameworks/index.d.ts"], "frameworks/nest": ["./dist/esm/frameworks/nest/index.d.ts"], "core/nest": ["./dist/esm/core/nest/index.d.ts"], "core/hono": ["./dist/esm/core/hono/index.d.ts"], "core/web": ["./dist/esm/core/web/index.d.ts"], "core": ["./dist/esm/core/index.d.ts"]}}, "files": ["dist", "!**/*.tsbuildinfo", "package.json", "README.md"], "publishConfig": {"access": "public"}, "dependencies": {"tslib": "^2.3.0", "express": "^4.21.2", "@nestjs/common": "^10.0.2", "@the-forgebase/database": "workspace:*", "@the-forgebase/storage": "workspace:*", "knex": "^3.1.0", "@nestjs/core": "^10.0.2", "itty-router": "^5.0.18", "@the-forgebase/auth": "workspace:*", "typescript": "5.8.2", "ultimate-express": "^1.4.6"}, "peerDependencies": {"@the-forgebase/database": "workspace:*", "@the-forgebase/storage": "workspace:*", "@the-forgebase/auth": "workspace:*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@the-forgebase/typescript-config": "workspace:*", "tslib": "^2.8.1", "@types/node": "~18.16.9"}}