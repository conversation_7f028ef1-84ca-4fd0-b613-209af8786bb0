{"name": "@the-forgebase/common", "version": "0.1.0-alpha.1", "type": "module", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/esm/index.d.ts", "scripts": {"clean": "rm -rf dist", "build:esm": "tsc -p tsconfig.esm.json", "build:cjs": "tsc -p tsconfig.cjs.json", "build": "pnpm clean && pnpm build:esm && pnpm build:cjs", "dev": "tsc -p tsconfig.esm.json --watch", "lint": "tsc --noEmit"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "typesVersions": {"*": {".": ["./dist/esm/index.d.ts"]}}, "files": ["dist", "!**/*.tsbuildinfo", "package.json", "README.md"], "devDependencies": {"@repo/eslint-config": "workspace:*", "@the-forgebase/typescript-config": "workspace:*", "tslib": "^2.8.1"}, "dependencies": {"typescript": "5.8.2"}}