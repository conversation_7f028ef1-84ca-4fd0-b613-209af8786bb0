{"extends": "@the-forgebase/typescript-config/base.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "forceConsistentCasingInFileNames": true, "strict": true, "importHelpers": true, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitAny": false, "noFallthroughCasesInSwitch": true, "noPropertyAccessFromIndexSignature": true, "types": ["node"], "jsx": "react", "composite": true}, "include": ["src"], "exclude": ["node_modules", "dist", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}