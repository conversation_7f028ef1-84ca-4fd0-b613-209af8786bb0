{"name": "@the-forgebase/react-native-auth", "version": "0.0.0-alpha.22", "private": false, "type": "commonjs", "main": "./src/index.js", "module": "./dist/index.js", "types": "./src/index.d.ts", "description": "React Native/Expo authentication SDK for ForgeBase", "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "typesVersions": {"*": {".": ["./dist/index.d.ts"]}}, "files": ["dist", "!**/*.tsbuildinfo"], "peerDependencies": {"react": ">=16.8.0", "react-native": "0.76.3", "tslib": "^2.4.0"}, "dependencies": {"axios": "^1.6.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-native": "^0.72.0", "typescript": "^5.0.0", "jest": "^29.5.0", "@types/jest": "^29.5.0", "@repo/eslint-config": "workspace:*", "@the-forgebase/typescript-config": "workspace:*"}, "keywords": ["react-native", "expo", "authentication", "auth", "forgebase"]}