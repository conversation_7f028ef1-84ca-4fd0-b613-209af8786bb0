{"extends": "@the-forgebase/typescript-config/base.json", "compilerOptions": {"module": "commonjs", "forceConsistentCasingInFileNames": true, "strict": true, "importHelpers": true, "noImplicitOverride": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noPropertyAccessFromIndexSignature": true, "declaration": true, "types": ["node"], "jsx": "react-native", "esModuleInterop": true}, "include": ["src/**/*.ts", "src/hooks.tsx"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "node_modules", "dist"]}