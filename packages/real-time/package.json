{"name": "@the-forgebase/real-time", "version": "0.0.0-alpha.22", "type": "module", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/esm/index.d.ts", "scripts": {"clean": "rm -rf dist", "build:esm": "tsc -p tsconfig.esm.json", "build:cjs": "tsc -p tsconfig.cjs.json", "build": "pnpm clean && pnpm build:esm && pnpm build:cjs", "dev": "tsc -p tsconfig.esm.json --watch", "lint": "tsc --noEmit"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "files": ["dist", "!**/*.tsbuildinfo", "package.json", "README.md"], "publishConfig": {"access": "public"}, "dependencies": {"tslib": "^2.3.0", "@cloudflare/workers-types": "^4.20250426.0", "ws": "^8.18.1", "uWebSockets.js": "https://github.com/uNetworking/uWebSockets.js/archive/refs/tags/v20.51.0.tar.gz", "uncrypto": "^0.1.3", "lib0": "^0.2.104", "yjs": "^13.6.26", "y-protocols": "^1.0.6", "y-leveldb": "^0.2.0", "typescript": "5.8.2"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@the-forgebase/typescript-config": "workspace:*", "@types/deno": "^2.2.0", "@types/bun": "^1.2.10", "@types/node": "~18.16.9", "@cloudflare/workers-types": "^4.20250426.0", "@types/web": "^0.0.226"}}