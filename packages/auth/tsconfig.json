{"extends": "@the-forgebase/typescript-config/base.json", "compilerOptions": {"rootDir": "./", "jsx": "react-jsx", "experimentalDecorators": true, "emitDecoratorMetadata": true, "noImplicitAny": false, "composite": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "types": ["deno"]}, "include": ["src", "scripts", "knexfile.ts", "migrations"], "exclude": ["node_modules", "dist", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}