{"name": "@the-forgebase/auth", "version": "0.1.0-alpha.1", "type": "module", "main": "./dist/cjs/src/index.js", "module": "./dist/esm/src/index.js", "types": "./dist/esm/src/index.d.ts", "bin": {"forgebase-init-migrations": "./dist/esm/scripts/init-migrations.js"}, "scripts": {"clean": "rm -rf dist", "build:esm": "tsc -p tsconfig.esm.json", "build:cjs": "tsc -p tsconfig.cjs.json", "build": "pnpm clean && pnpm build:esm && pnpm build:cjs", "dev": "pnpm build:esm --watch", "lint": "tsc --noEmit", "migrate": "tsx scripts/migrate.ts", "migrate:make": "tsx scripts/make-migration.ts", "migrate:rollback": "tsx scripts/rollback.ts", "postinstall": "chmod +x ./dist/esm/scripts/*.js"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/esm/src/index.d.ts", "import": "./dist/esm/src/index.js", "require": "./dist/cjs/src/index.js"}, "./adapters/nest": {"types": "./dist/esm/src/adapters/nest/index.d.ts", "import": "./dist/esm/src/adapters/nest/index.js", "require": "./dist/cjs/src/adapters/nest/index.js"}, "./adapters/web": {"types": "./dist/esm/src/adapters/web/index.d.ts", "import": "./dist/esm/src/adapters/web/index.js", "require": "./dist/cjs/src/adapters/web/index.js"}, "./adapters/express": {"types": "./dist/esm/src/adapters/express/index.d.ts", "import": "./dist/esm/src/adapters/express/index.js", "require": "./dist/cjs/src/adapters/express/index.js"}, "./adapters/ultimate-express": {"types": "./dist/esm/src/adapters/ultimate-express/index.d.ts", "import": "./dist/esm/src/adapters/ultimate-express/index.js", "require": "./dist/cjs/src/adapters/ultimate-express/index.js"}}, "typesVersions": {"*": {".": ["./dist/esm/src/index.d.ts"], "adapters/nest": ["./dist/esm/src/adapters/nest/index.d.ts"], "adapters/web": ["./dist/esm/src/adapters/web/index.d.ts"], "adapters/express": ["./dist/esm/src/adapters/express/index.d.ts"], "adapters/ultimate-express": ["./dist/esm/src/adapters/ultimate-express/index.d.ts"]}}, "files": ["dist", "!**/*.tsbuildinfo", "package.json", "README.md"], "devDependencies": {"@repo/eslint-config": "workspace:*", "@the-forgebase/typescript-config": "workspace:*", "tslib": "^2.8.1", "@types/deno": "^2.2.0"}, "dependencies": {"@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@the-forgebase/common": "workspace:*", "arctic": "^3.6.1", "awilix": "^12.0.5", "axios": "^1.9.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "express": "^5.1.0", "itty-router": "^5.0.18", "jsonwebtoken": "^9.0.2", "jsx-email": "^2.7.2", "knex": "^3.1.0", "knex-mock-client": "^3.0.2", "nodemailer": "^7.0.3", "qrcode": "^1.5.4", "react": "^19.1.0", "rfc4648": "^1.5.4", "speakeasy": "^2.0.0", "twilio": "^5.6.0", "typescript": "5.8.2", "ultimate-express": "^1.4.9", "uuid": "^11.1.0", "zod": "^3.24.4"}}