# @the-forgebase/auth

## 0.1.0-alpha.1

### Patch Changes

- [`c9e9816`](https://github.com/The-ForgeBase/forgebase-ts/commit/c9e9816906acf1d80a3c6134ef87a14d52a8d521) Thanks [@SOG-web](https://github.com/SOG-web)! - testing patch alpha

- [`baa319f`](https://github.com/The-ForgeBase/forgebase-ts/commit/baa319f22521d3ae3e9877a24da90ea15650330c) Thanks [@SOG-web](https://github.com/SOG-web)! - final

- Updated dependencies [[`c9e9816`](https://github.com/The-ForgeBase/forgebase-ts/commit/c9e9816906acf1d80a3c6134ef87a14d52a8d521), [`baa319f`](https://github.com/The-ForgeBase/forgebase-ts/commit/baa319f22521d3ae3e9877a24da90ea15650330c)]:
  - @the-forgebase/common@0.1.0-alpha.1

## 0.1.0-alpha.0

### Minor Changes

- [`235d622`](https://github.com/The-ForgeBase/forgebase-ts/commit/235d622f193467f15ac5ce21aa1c17cc02a4afc8) Thanks [@SOG-web](https://github.com/SOG-web)! - testing alpha

### Patch Changes

- Updated dependencies [[`235d622`](https://github.com/The-ForgeBase/forgebase-ts/commit/235d622f193467f15ac5ce21aa1c17cc02a4afc8)]:
  - @the-forgebase/common@0.1.0-alpha.0

## 0.0.1

### Patch Changes

- [`a733176`](https://github.com/The-ForgeBase/forgebase-ts/commit/a7331764dfeb8160fbb74bcda66cea8aceed8ee0) Thanks [@SOG-web](https://github.com/SOG-web)! - testing versions

- Updated dependencies [[`a733176`](https://github.com/The-ForgeBase/forgebase-ts/commit/a7331764dfeb8160fbb74bcda66cea8aceed8ee0)]:
  - @the-forgebase/common@0.0.1

## 0.0.0

### Minor Changes

- [#108](https://github.com/The-ForgeBase/forgebase-ts/pull/108) [`8a2996d`](https://github.com/The-ForgeBase/forgebase-ts/commit/8a2996d40d0038dd244609d56abed57b6d8b6b3d) Thanks [@SOG-web](https://github.com/SOG-web)! - refactor(all): update TypeScript config and package exports

  - Update tsconfig.cjs.json to use CommonJS with proper module resolution
  - Add typesVersions field to all packages for better TypeScript support
  - Update auth package exports to include adapter type definitions
  - Fix package.json exports to point to correct source paths
  - Add skipLibCheck and esModuleInterop for better compatibility

### Patch Changes

- Updated dependencies [[`8a2996d`](https://github.com/The-ForgeBase/forgebase-ts/commit/8a2996d40d0038dd244609d56abed57b6d8b6b3d)]:
  - @the-forgebase/common@0.0.0
