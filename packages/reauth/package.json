{"name": "@the-forgebase/reauth", "version": "0.1.0-alpha.1", "type": "module", "main": "./dist/cjs/src/index.js", "module": "./dist/esm/src/index.js", "types": "./dist/esm/src/index.d.ts", "scripts": {"clean": "rm -rf dist", "build:esm": "tsc -p tsconfig.esm.json", "build:cjs": "tsc -p tsconfig.cjs.json", "build": "pnpm clean && pnpm build:esm && pnpm build:cjs", "dev": "pnpm build:esm --watch", "lint": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/esm/src/index.d.ts", "import": "./dist/esm/src/index.js", "require": "./dist/cjs/src/index.js"}, "./plugins/utils": {"types": "./dist/esm/src/plugins/utils/index.d.ts", "import": "./dist/esm/src/plugins/utils/index.js", "require": "./dist/cjs/src/plugins/utils/index.js"}, "./plugins/email-password": {"types": "./dist/esm/src/plugins/email-password/email-password.plugin.d.ts", "import": "./dist/esm/src/plugins/email-password/email-password.plugin.js", "require": "./dist/cjs/src/plugins/email-password/email-password.plugin.js"}, "./services": {"types": "./dist/esm/src/services/index.d.ts", "import": "./dist/esm/src/services/index.js", "require": "./dist/cjs/src/services/index.js"}}, "typesVersions": {"*": {".": ["./dist/esm/src/index.d.ts"], "plugins/utils": ["./dist/esm/src/plugins/utils/index.d.ts"], "plugins/email-password": ["./dist/esm/src/plugins/email-password/email-password.plugin.d.ts"], "services": ["./dist/esm/src/services/index.d.ts"]}}, "files": ["dist", "!**/*.tsbuildinfo", "package.json", "README.md"], "devDependencies": {"@repo/eslint-config": "workspace:*", "@the-forgebase/typescript-config": "workspace:*", "@types/deno": "^2.2.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.21", "tslib": "^2.8.1", "vitest": "^3.1.4"}, "dependencies": {"@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@standard-schema/spec": "^1.0.0", "arctic": "^3.6.1", "arktype": "^2.1.20", "awilix": "^12.0.5", "axios": "^1.9.0", "express": "^5.1.0", "itty-router": "^5.0.18", "jsonwebtoken": "^9.0.2", "jsx-email": "^2.7.2", "knex": "^3.1.0", "qrcode": "^1.5.4", "react": "^19.1.0", "rfc4648": "^1.5.4", "typescript": "^5.8.3", "ultimate-express": "^2.0.4", "uuid": "^11.1.0"}}