import {
  AuthInput,
  AuthOutput,
  AuthPlugin,
  Entity,
  EntityService,
  HooksType,
  MigrationConfig,
  PluginNotFound,
  ReAuthCradle,
  SessionService,
  StepNotFound,
} from './types';
import {
  createContainer,
  InjectionMode,
  AwilixContainer,
  asValue,
} from 'awilix';
import { createHookRegisterer, executeStep } from './plugins/utils';

export class ReAuthEngine {
  private container: AwilixContainer<ReAuthCradle>;
  private plugins: AuthPlugin[] = [];
  private migrationConfig: MigrationConfig;

  constructor(config: {
    plugins: AuthPlugin[];
    entity: EntityService;
    session: SessionService;
  }) {
    this.container = createContainer<ReAuthCradle>({
      injectionMode: InjectionMode.CLASSIC,
      strict: true,
    });

    this.container.register({
      entityService: asValue(config.entity),
    });
    this.container.register({
      sessionService: asValue(config.session),
    });

    config.plugins.forEach((plugin) => this.registerPlugin(plugin));

    this.migrationConfig = {
      migrationName: 'reauth',
      outputDir: 'migrations',
      baseTables: [
        {
          tableName: 'entities',
          columns: {
            id: {
              type: 'uuid',
              primary: true,
              nullable: false,
              unique: true,
              defaultValue: 'uuid',
            },
            role: {
              type: 'string',
              nullable: false,
              defaultValue: 'user',
            },
          },
          timestamps: true,
        },
        {
          tableName: 'sessions',
          columns: {
            id: {
              type: 'uuid',
              primary: true,
              nullable: false,
              unique: true,
              defaultValue: 'uuid',
            },
            entity_id: {
              type: 'uuid',
              nullable: false,
            },
            token: {
              type: 'string',
              unique: true,
              nullable: false,
            },
            expires_at: {
              type: 'timestamp',
              nullable: true,
            },
          },
          timestamps: true,
        },
      ],
      plugins: config.plugins
        .map((plugin) => plugin.migrationConfig)
        .filter((config) => config !== undefined),
    };
  }

  getMirgrationCongfig(): MigrationConfig {
    return this.migrationConfig;
  }

  /**
   * Get a service from the DI container
   * @param name The name of the service to retrieve
   */
  getService<T extends keyof ReAuthCradle>(
    container: AwilixContainer<ReAuthCradle>,
    serviceName: T,
  ): ReAuthCradle[T] {
    return container.cradle[serviceName];
  }

  /**
   * Register an auth plugin
   * @param auth The auth plugin to register
   */
  registerPlugin(auth: AuthPlugin) {
    this.plugins.push(auth);
    auth.initialize(this.container);
    return this;
  }

  /**
   * Get a plugin by name
   * @param name The name of the plugin to retrieve
   */
  getPlugin(name: string) {
    const plugin = this.plugins.find((p) => p.name === name);
    if (!plugin) {
      throw new PluginNotFound(name);
    }
    return plugin;
  }

  /**
   * Get all registered plugins
   */
  getAllPlugins() {
    return this.plugins;
  }

  /**
   * Get the DI container
   */
  getContainer(): AwilixContainer<ReAuthCradle> {
    return this.container;
  }

  executeStep(pluginName: string, stepName: string, input: AuthInput) {
    const plugin = this.getPlugin(pluginName);
    const step = plugin.steps.find((s) => s.name === stepName);
    if (!step) {
      throw new StepNotFound(stepName, pluginName);
    }

    if (plugin.runStep) return plugin.runStep(step.name, input, this.container);

    return executeStep(stepName, input, {
      pluginName,
      step,
      container: this.container,
      config: plugin.config,
    });
  }

  registerHook(
    pluginName: string,
    stepName: string,
    type: HooksType,
    fn: (
      data: AuthInput | AuthOutput,
      container: AwilixContainer<ReAuthCradle>,
      error?: Error,
    ) => Promise<AuthOutput | AuthInput | void>,
  ) {
    const plugin = this.getPlugin(pluginName);
    const step = plugin.steps.find((s) => s.name === stepName);
    if (!step) {
      throw new StepNotFound(stepName, pluginName);
    }

    if (step.registerHook) {
      step.registerHook(type, fn);

      return this;
    }

    if (!step.hooks) step.hooks = {};

    const register = createHookRegisterer(step.hooks);
    register(type, fn);
    return this;
  }

  getStepInputs(pluginName: string, stepName: string) {
    const plugin = this.getPlugin(pluginName);
    const step = plugin.steps.find((s) => s.name === stepName);
    if (!step) {
      throw new StepNotFound(stepName, pluginName);
    }
    return step.inputs;
  }
}

export const createReAuthEngine = (config: {
  plugins: AuthPlugin[];
  entity: EntityService;
  session: SessionService;
}): ReAuthEngine => {
  return new ReAuthEngine(config);
};

const test: Entity = {
  id: '1',
  role: 'user',
  created_at: new Date(),
  updated_at: new Date(),
  email: '<EMAIL>',
  email_verified: false,
  password_hash: 'test',
};
