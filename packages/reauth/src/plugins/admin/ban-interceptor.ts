import { AwilixContainer } from 'awilix';
import { ReAuthEngine } from '../../auth-engine';
import { AuthInput, AuthOutput, ReAuthCradle } from '../../types';

/**
 * Error thrown when a banned user tries to authenticate
 */
export class UserBannedError extends Error {
  constructor(
    public entityId: string,
    public reason?: string,
    public bannedAt?: Date,
    public bannedBy?: string,
  ) {
    super(`User ${entityId} is banned${reason ? `: ${reason}` : ''}`);
    this.name = 'UserBannedError';
  }
}

/**
 * Registers ban check hooks on authentication steps across all plugins
 * This function should be called after all plugins are registered
 */
export function registerBanInterceptor(reAuthEngine: ReAuthEngine) {
  // Get all plugins
  const plugins = reAuthEngine.getAllPlugins();

  // Steps that should check for banned users (authentication-related steps)
  const authStepsToIntercept = [
    'login',
    'register',
    'verify-email',
    'reset-password',
    'change-password',
    'verify-magiclink',
    'verify-otp',
    'refresh-token',
  ];

  // Register hooks on relevant steps
  plugins.forEach((plugin) => {
    plugin.steps.forEach((step) => {
      if (authStepsToIntercept.includes(step.name)) {
        // Register a 'before' hook to check ban status
        reAuthEngine.registerHook(
          plugin.name,
          step.name,
          'before',
          async (
            data: AuthInput | AuthOutput,
            container: AwilixContainer<ReAuthCradle>,
          ) => {
            const input = data as AuthInput;

            // Check if we have an entity to check
            let entityId: string | undefined;

            // Try to get entity ID from various sources
            if (input.entity?.id) {
              entityId = input.entity.id;
            } else if (input.email) {
              // For email-based auth, look up the entity
              try {
                const entityService = container.cradle.entityService;
                const entity = await entityService.findEntity(
                  input.email,
                  'email',
                );
                entityId = entity?.id;
              } catch (error) {
                // If we can't find the entity, let the original step handle it
                return input;
              }
            } else if (input.phone) {
              // For phone-based auth, look up the entity
              try {
                const entityService = container.cradle.entityService;
                const entity = await entityService.findEntity(
                  input.phone,
                  'phone',
                );
                entityId = entity?.id;
              } catch (error) {
                // If we can't find the entity, let the original step handle it
                return input;
              }
            }

            // If we have an entity ID, check ban status
            if (entityId) {
              try {
                const banCheckService = container.cradle.banCheckService;
                if (banCheckService) {
                  const banInfo =
                    await banCheckService.checkBanStatus(entityId);

                  if (banInfo && banInfo.banned) {
                    throw new UserBannedError(
                      entityId,
                      banInfo.reason,
                      banInfo.banned_at,
                      banInfo.banned_by,
                    );
                  }
                }
              } catch (error) {
                if (error instanceof UserBannedError) {
                  throw error;
                }
                // If ban check fails, log but don't block authentication
                console.warn('Ban check failed:', error);
              }
            }

            return input;
          },
        );
      }
    });
  });
}

/**
 * Convenience function to register ban interceptor for specific plugins and steps
 */
export function registerSelectiveBanInterceptor(
  reAuthEngine: ReAuthEngine,
  interceptConfig: Array<{
    pluginName: string;
    stepNames: string[];
  }>,
) {
  interceptConfig.forEach(({ pluginName, stepNames }) => {
    stepNames.forEach((stepName) => {
      try {
        reAuthEngine.registerHook(
          pluginName,
          stepName,
          'before',
          async (
            data: AuthInput | AuthOutput,
            container: AwilixContainer<ReAuthCradle>,
          ) => {
            const input = data as AuthInput;

            // Extract entity ID logic (same as above)
            let entityId: string | undefined;

            if (input.entity?.id) {
              entityId = input.entity.id;
            } else if (input.email) {
              try {
                const entityService = container.cradle.entityService;
                const entity = await entityService.findEntity(
                  input.email,
                  'email',
                );
                entityId = entity?.id;
              } catch (error) {
                return input;
              }
            } else if (input.phone) {
              try {
                const entityService = container.cradle.entityService;
                const entity = await entityService.findEntity(
                  input.phone,
                  'phone',
                );
                entityId = entity?.id;
              } catch (error) {
                return input;
              }
            }

            if (entityId) {
              try {
                const banCheckService = container.cradle.banCheckService;
                if (banCheckService) {
                  const banInfo =
                    await banCheckService.checkBanStatus(entityId);

                  if (banInfo && banInfo.banned) {
                    throw new UserBannedError(
                      entityId,
                      banInfo.reason,
                      banInfo.banned_at,
                      banInfo.banned_by,
                    );
                  }
                }
              } catch (error) {
                if (error instanceof UserBannedError) {
                  throw error;
                }
                console.warn('Ban check failed:', error);
              }
            }

            return input;
          },
        );
      } catch (error) {
        console.warn(
          `Failed to register ban interceptor for ${pluginName}.${stepName}:`,
          error,
        );
      }
    });
  });
}
