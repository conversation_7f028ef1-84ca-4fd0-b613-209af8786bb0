import { AuthPlugin, AuthStep } from '../../types';

export function createAuthPlugin(
  config: Partial<AuthPlugin>,
  plugin: AuthPlugin,
  overideStep?: {
    name: string;
    overide: Partial<AuthStep<any>>;
  }[],
): AuthPlugin {
  const basePlugin = plugin;

  if (overideStep && overideStep.length > 0) {
    const newSteps = overideStep
      .map((stp) => {
        const exist = basePlugin.steps.find((s) => s.name === stp.name);
        if (!exist) return;

        return { ...exist, ...stp.overide };
      })
      .filter((s) => s !== undefined);

    if (!config.steps) {
      config.steps = [...newSteps];
    } else {
      const fs = [...newSteps, ...config.steps];
      config.steps = fs;
    }
  }

  //TODO: merge the steps, allow overide
  const baseSteps = basePlugin.steps;
  const newSteps = config.steps;

  let finalSteps: AuthStep<any>[] = [];

  if (newSteps) {
    const reStep = baseSteps
      .map((os) => {
        const old = newSteps.find((s) => s.name === os.name);
        if (old) {
          return;
        }

        return os;
      })
      .filter((s) => s !== undefined);

    finalSteps = [...reStep, ...newSteps];
  } else {
    finalSteps = baseSteps;
  }

  const combinedPlugin: AuthPlugin = {
    ...basePlugin,
    ...config,
    steps: finalSteps,
  };
  return combinedPlugin;
}
