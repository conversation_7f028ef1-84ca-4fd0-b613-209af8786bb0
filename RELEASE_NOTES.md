## What's Changed


add ESM and CJS dual package support


add ESM and CJS dual package support


add ESM and CJS dual package support

add ESM and CJS dual package support

### Commits
* feat(auth): add ESM and CJS dual package support (0217808)
* feat(auth): add ESM and CJS dual package support (b02ef87)
* feat(auth): add ESM and CJS dual package support (8436702)
* feat(auth): add ESM and CJS dual package support (a6bb504)
* feat(auth): add ESM and CJS dual package support (37bf759)
* feat(auth): add ESM and CJS dual package support (ef47585)
* feat(auth): add ESM and CJS dual package support (b58d27e)
* Merge pull request #92 from The-ForgeBase/turbo (768618b)
* feat(auth): add ESM and CJS dual package support (040b5b0)
* Merge pull request #91 from The-ForgeBase/turbo (59c4327)
* feat(auth): add ESM and CJS dual package support (4c2b6c4)
* Merge pull request #90 from The-ForgeBase/turbo (825cc7f)
* feat(auth): add ESM and CJS dual package support (69952f7)
* feat(auth): add ESM and CJS dual package support (3f0dcf9)
* feat(auth): add ESM and CJS dual package support (93f9524)
* feat(auth): add ESM and CJS dual package support (0df62cf)
* chore: add configuration files and remove unused files (96dc0d2)
* moving to turbo-repo (e78977d)
* feat(create-turbo): install dependencies (b281d6f)
* feat(create-turbo): apply pnpm-eslint transform (a1f7180)
* feat(create-turbo): apply official-starter transform (78209a7)
* moving to turborepo (56cf8a7)
* moving with agx (cb051db)
* feat(docs): add documentation framework with content and layout components (6d7849c)
* container is having error (d467d41)
* more cleanup (64f6754)
* refactor(auth): remove async from initializeContainer and related calls (4d0c4a8)
* refactor(auth): remove rate limiter and update provider types (fb34973)
* refactor(auth): simplify provider registration and initialization (ddf47c6)
* feat(auth): add Google OAuth provider and update README for dependency injection (178e339)
* refactor(auth): migrate to dependency injection with Awilix container (0e27964)
* refactor(auth): restructure constructor parameter assignments for better maintainability (75ae21e)
* refactor(auth): restructure auth module and remove unused code (15d138a)