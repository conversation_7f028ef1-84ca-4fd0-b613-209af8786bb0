{"name": "hono-test", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@hono/node-server": "^1.14.2", "@the-forgebase/reauth": "workspace:*", "better-sqlite3": "^11.10.0", "hono": "^4.7.10", "knex": "^3.1.0"}, "devDependencies": {"@the-forgebase/typescript-config": "workspace:*", "@types/node": "^20.11.17", "esbuild": "^0.25.4", "ts-node": "^10.9.2", "tsx": "^4.7.1", "typescript": "^5.8.3"}}