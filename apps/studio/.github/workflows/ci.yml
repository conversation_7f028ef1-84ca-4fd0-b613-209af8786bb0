name: CI

on:
  push:
    branches:
      - main

  pull_request:
    branches:
      - main

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - id: checkout
        name: Checkout
        uses: actions/checkout@v4

      - id: setup-ni
        name: Setup
        run: npm i -g @antfu/ni

      - id: install-deps
        name: Install dependencies
        run: nci

      - name: Lint
        run: nr lint
