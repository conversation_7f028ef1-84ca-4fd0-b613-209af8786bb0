{"name": "nuxt-shadcn-dashboard", "type": "module", "version": "0.2.6", "description": "Shadcn Dashboard built with Nuxt", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://dashboard.dianprata.com/", "repository": {"type": "git", "url": "git+https://github.com/dianprata/nuxt-shadcn-dashboard.git"}, "keywords": ["dashboard", "admin-dashboard", "vue", "nuxt", "shadcn-vue", "shadcn-nuxt", "shadcn-dashboard", "unocss"], "main": "./nuxt.config.ts", "files": ["app.vue", "components", "components.json", "composables", "constants", "layouts", "lib", "nuxt.config.ts", "pages", "types"], "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "typecheck": "vue-tsc --noEmit", "lint": "eslint .", "format": "eslint --fix ."}, "dependencies": {"@iconify-json/lucide": "^1.2.30", "@iconify-json/radix-icons": "^1.2.2", "@internationalized/date": "^3.7.0", "@number-flow/vue": "^0.4.5", "@tanstack/vue-table": "^8.21.2", "@unovis/ts": "^1.5.1", "@unovis/vue": "^1.5.1", "embla-carousel": "^8.5.2", "embla-carousel-vue": "^8.5.2"}, "devDependencies": {"@antfu/eslint-config": "^4.10.1", "@nuxt/eslint": "^1.2.0", "@nuxt/icon": "^1.11.0", "@nuxtjs/color-mode": "^3.5.2", "@pinia/nuxt": "^0.5.1", "@unocss/eslint-plugin": "^66.0.0", "@unocss/nuxt": "^66.0.0", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^12.2.0", "@vueuse/math": "^12.2.0", "@vueuse/nuxt": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "eslint": "^9.22.0", "eslint-plugin-format": "^1.0.1", "lucide-vue-next": "^0.482.0", "nuxt": "^3.16.2", "radix-vue": "^1.9.17", "shadcn-nuxt": "^0.10.4", "tailwind-merge": "^2.6.0", "typescript": "^5.8.2", "unocss": "^66.0.0", "unocss-preset-animations": "^1.1.1", "unocss-preset-shadcn": "^0.5.0", "vaul-vue": "^0.2.0", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-sonner": "^1.3.0", "vue-tsc": "^2.1.10", "zod": "^3.24.2"}}