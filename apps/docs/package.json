{"name": "shadcn-docs-nuxt-starter", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "1.4.0", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.13.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.6", "@unhead/vue": "^2.0.3", "eslint": "^9.0.0", "nuxt": "^3.17.0", "shadcn-docs-nuxt": "^0.8.21", "typescript": "^5.6.3", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17"}}