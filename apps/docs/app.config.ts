export default defineAppConfig({
  shadcnDocs: {
    site: {
      name: 'ForgeBase',
      description:
        'ForgeBase is an open-source Backend as a Service (BaaS) framework designed to provide backend functionality for a variety of backend frameworks across multiple languages. This TypeScript implementation uses [Nx](https://nx.dev) as a monorepo management tool to organize the codebase into multiple libraries and applications.',
    },
    theme: {
      customizable: true,
      color: 'gray',
      radius: 0.5,
    },
    banner: {
      enable: true,
      showClose: false,
      content: 'ForgeBase is in Alpha development. !Use at your own risk!',
      to: 'https://github.com/the-forgebase/forgebase-ts',
      target: '_blank',
      border: true,
    },
    header: {
      title: 'ForgeBase',
      showTitle: true,
      darkModeToggle: true,
      logo: {
        light: '/logo.svg',
        dark: '/logo-dark.svg',
      },
      nav: [
        {
          title: 'Getting Started',
          to: '/getting-started',
          icon: 'lucide:rocket',
          target: '_self',
          description: 'Get started with ForgeBase',
        },
        {
          title: 'Packages',
          icon: 'lucide:package',
          links: [
            {
              title: 'Database',
              icon: 'lucide:database',
              to: '/database',
              target: '_self',
              description: 'Explore ForgeBase database and it functionality',
            },
            {
              title: 'API',
              to: '/api',
              target: '_self',
              icon: 'lucide:globe',
              description: 'Explore ForgeBase API and it functionality',
            },
            {
              title: 'Client Packages',
              to: '/client-packages',
              target: '_self',
              icon: 'lucide:package-open',
              description:
                'Explore ForgeBase client packages for web and mobile applications',
            },
          ],
        },
      ],
      links: [
        {
          icon: 'lucide:github',
          to: 'https://github.com/the-forgebase/forgebase-ts',
          target: '_blank',
        },
      ],
    },
    aside: {
      useLevel: true,
      collapse: true,
      collapseLevel: 1,
      folderStyle: 'default',
    },
    main: {
      breadCrumb: true,
      showTitle: true,
      codeCopyToast: true,
      codeCopyIcon: 'lucide:clipboard',
      editLink: {
        enable: true,
        pattern: 'https://github.com/the-forgebase/forgebase-ts',
        text: 'Edit this page',
        icon: 'lucide:square-pen',
        placement: ['docsFooter'],
      },
      backToTop: true,
      codeIcon: {
        'package.json': 'vscode-icons:file-type-node',
        'tsconfig.json': 'vscode-icons:file-type-tsconfig',
        '.npmrc': 'vscode-icons:file-type-npm',
        '.editorconfig': 'vscode-icons:file-type-editorconfig',
        '.eslintrc': 'vscode-icons:file-type-eslint',
        '.eslintrc.cjs': 'vscode-icons:file-type-eslint',
        '.eslintignore': 'vscode-icons:file-type-eslint',
        'eslint.config.js': 'vscode-icons:file-type-eslint',
        'eslint.config.mjs': 'vscode-icons:file-type-eslint',
        'eslint.config.cjs': 'vscode-icons:file-type-eslint',
        '.gitignore': 'vscode-icons:file-type-git',
        'yarn.lock': 'vscode-icons:file-type-yarn',
        '.env': 'vscode-icons:file-type-dotenv',
        '.env.example': 'vscode-icons:file-type-dotenv',
        '.vscode/settings.json': 'vscode-icons:file-type-vscode',
        nuxt: 'vscode-icons:file-type-nuxt',
        '.nuxtrc': 'vscode-icons:file-type-nuxt',
        '.nuxtignore': 'vscode-icons:file-type-nuxt',
        'nuxt.config.js': 'vscode-icons:file-type-nuxt',
        'nuxt.config.ts': 'vscode-icons:file-type-nuxt',
        'nuxt.schema.ts': 'vscode-icons:file-type-nuxt',
        'tailwind.config.js': 'vscode-icons:file-type-tailwind',
        'tailwind.config.ts': 'vscode-icons:file-type-tailwind',
        vue: 'vscode-icons:file-type-vue',
        ts: 'vscode-icons:file-type-typescript',
        tsx: 'vscode-icons:file-type-typescript',
        mjs: 'vscode-icons:file-type-js',
        cjs: 'vscode-icons:file-type-js',
        js: 'vscode-icons:file-type-js',
        jsx: 'vscode-icons:file-type-js',
        md: 'vscode-icons:file-type-markdown',
        mdc: 'vscode-icons:file-type-markdown',
        css: 'vscode-icons:file-type-css',
        py: 'vscode-icons:file-type-python',
        npm: 'vscode-icons:file-type-npm',
        pnpm: 'vscode-icons:file-type-pnpm',
        npx: 'vscode-icons:file-type-npm',
        yarn: 'vscode-icons:file-type-yarn',
        bun: 'vscode-icons:file-type-bun',
        yml: 'vscode-icons:file-type-light-yaml',
        json: 'vscode-icons:file-type-json',
        terminal: 'lucide:terminal',
        http: 'lucide:globe',
        git: 'vscode-icons:file-type-git',
      },
    },
    footer: {
      credits: 'Copyright © 2025',
      links: [
        // {
        //   icon: 'lucide:heart',
        //   title: 'Sponsor Me',
        //   to: 'https://ko-fi.com/ztl_uwu',
        //   target: '_blank',
        // },
        {
          icon: 'lucide:twitter',
          to: 'https://x.com/SOGtheImmortal',
          target: '_blank',
        },
        {
          icon: 'lucide-lab:butterfly',
          to: 'https://bsky.app/profile/sogtheimmortal.bsky.social',
          target: '_blank',
        },
        // {
        //   icon: 'simple-icons:discord',
        //   to: 'https://discord.gg/9P5HzAz8DT',
        //   target: '_blank',
        // },
        {
          icon: 'lucide:github',
          to: 'https://github.com/the-forgebase/forgebase-ts',
          target: '_blank',
        },
      ],
    },
    toc: {
      enable: true,
      title: 'On This Page',
      links: [
        {
          title: 'Star on GitHub',
          icon: 'lucide:star',
          to: 'https://github.com/the-forgebase/forgebase-ts',
          target: '_blank',
        },
        {
          title: 'Create Issues',
          icon: 'lucide:circle-dot',
          to: 'https://github.com/the-forgebase/forgebase-ts/issues',
          target: '_blank',
        },
      ],
      iconLinks: [
        // {
        //   icon: 'lucide:coffee',
        //   to: 'https://ko-fi.com/ztl_uwu',
        //   target: '_blank',
        // },
        {
          icon: 'lucide:twitter',
          to: 'https://x.com/SOGtheImmortal',
          target: '_blank',
        },
        {
          icon: 'lucide-lab:butterfly',
          to: 'https://bsky.app/profile/sogtheimmortal.bsky.social',
          target: '_blank',
        },
        // {
        //   icon: 'simple-icons:discord',
        //   to: 'https://discord.gg/9P5HzAz8DT',
        //   target: '_blank',
        // },
      ],
    },
    search: {
      enable: true,
      inAside: false,
      style: 'input',
    },
  },
});
