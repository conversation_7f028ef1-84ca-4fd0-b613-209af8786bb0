---
title: Authentication
description: Implement authentication with the ForgeBase API package
icon: 'lucide:lock'
---

ForgeBase API integrates seamlessly with ForgeBase Auth to provide authentication and authorization for your application. This page explains how to implement authentication with the API package.

## Overview

Authentication in ForgeBase API works by:

1. **User Context**: The API uses a user context object to determine the current user's identity and permissions
2. **Row-Level Security**: The user context is used to enforce row-level security in the database
3. **Framework Integration**: Each framework adapter handles authentication in a way that's natural for that framework

## User Context

The user context is an object that contains information about the current user:

```typescript [ts]
interface UserContext {
  userId: string | number; // The user's ID
  role?: string; // The user's role (optional)
  teams?: string[]; // The teams the user belongs to (optional)
  labels?: string[]; // The user's labels (optional)
  [key: string]: any; // Additional custom properties
}
```

This context is passed to database operations to enforce row-level security.

## Express Integration

### Setting Up Authentication

To integrate authentication with Express, you can use the ForgeBase Auth package:

```typescript [ts]
import express from 'express';
import { forgeApi } from '@the-forgebase/api';
import { createExpressAuthClient, initializeExpressAuth } from '@the-forgebase/auth/adapters/express';

// Create Express app
const app = express();
app.use(express.json());

// Configure auth
const { container, config } = createExpressAuthClient({
  config: {
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    cookieSecret: process.env.COOKIE_SECRET || 'your-cookie-secret',
    tokenExpiration: '1h',
    refreshTokenExpiration: '7d',
    cookieSecure: process.env.NODE_ENV === 'production',
  },
  deps: {
    // Your auth dependencies
  },
});

// Initialize auth
const { authRouter, adminRouter } = initializeExpressAuth(container.cradle.authManager, container.cradle.adminManager, config);

// Mount auth routes
app.use('/auth', authRouter);
app.use('/admin', adminRouter);

// Initialize ForgeBase API
const api = forgeApi({
  prefix: '/api',
  services: {
    db: {
      provider: 'sqlite',
      config: {
        filename: './mydb.sqlite',
        enforceRls: true,
      },
    },
  },
});

// Mount the ForgeBase API
app.use('/api', api.handle);

// Start the server
app.listen(3000);
```

### User Context Middleware

The Express adapter automatically extracts the user context from the request:

```typescript [ts]
import { userContextMiddleware } from '@the-forgebase/auth/adapters/express';

// Add user context middleware
app.use(async (req, res, next) => {
  await userContextMiddleware(req, res, next, container.cradle.authManager, config, container.cradle.adminManager);
});
```

## Hono Integration

### Setting Up Authentication

To integrate authentication with Hono, you can use the ForgeBase Auth package:

```typescript [ts]
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { forgeApi } from '@the-forgebase/api';
import { HonoAdapter } from '@the-forgebase/api/adapters';
import { createAuthContainer } from '@the-forgebase/auth';
import { webAuthApi } from '@the-forgebase/auth/adapters/web';

// Create Hono app
const app = new Hono();

// Configure CORS
app.use(
  '*',
  cors({
    origin: 'http://localhost:5173',
    credentials: true,
  }),
);

// Create auth container
const container = createAuthContainer({
  // Your auth dependencies
});

// Create auth API
const authApi = webAuthApi({
  container,
  config: {
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    cookieSecret: process.env.COOKIE_SECRET || 'your-cookie-secret',
    tokenExpiration: '1h',
    refreshTokenExpiration: '7d',
    cookieSecure: process.env.NODE_ENV === 'production',
  },
  cors: {
    enabled: true,
    corsOptions: {
      origin: ['http://localhost:5173'],
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
    },
  },
});

// Handle auth requests
app.all('/auth/*', async (c) => {
  try {
    const response = await authApi.handleRequest(c.req.raw);
    return new Response(response.body, {
      status: response.status,
      headers: response.headers,
    });
  } catch (error) {
    return c.json({ error: error.message }, 500);
  }
});

// Initialize ForgeBase API
const api = forgeApi({
  prefix: '/api',
  services: {
    db: {
      provider: 'sqlite',
      config: {
        filename: './mydb.sqlite',
        enforceRls: true,
      },
    },
  },
});

// Apply ForgeBase API to specific routes
app.use('/api/*', async (c) => {
  try {
    const adapter = new HonoAdapter(c);
    const result = await api.handle(adapter);

    if (result) {
      const { context } = result;
      return c.json(context.res.body, context.res.status);
    }
    return c.next();
  } catch (error) {
    return c.json({ error: error.message }, 500);
  }
});

// Export for serverless environments
export default {
  port: 3000,
  fetch: app.fetch,
};
```

### User Context Middleware

The Hono adapter can extract the user context from the request:

```typescript [ts]
import { userContextMiddleware } from '@the-forgebase/auth/adapters/web';

// Add user context middleware
app.use('*', async (c, next) => {
  // Extract user context from request
  await userContextMiddleware(c.req.raw, container.cradle.authManager, config, container.cradle.adminManager);

  // Store session in context
  c.set('session', {
    userContext: c.get('userContext'),
    isAdmin: c.get('isAdmin'),
    isSystem: c.get('isSystem'),
  });

  return next();
});
```

## NestJS Integration

### Setting Up Authentication

To integrate authentication with NestJS, you can use the ForgeBase Auth package:

```typescript [ts]
import { Module } from '@nestjs/common';
import { ForgeApiModule } from '@the-forgebase/api/core/nest';
import { NestAuthModule } from '@the-forgebase/auth/adapters/nest';

@Module({
  imports: [
    // ForgeBase Auth module
    NestAuthModule.forRoot({
      jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
      cookieSecret: process.env.COOKIE_SECRET || 'your-cookie-secret',
      tokenExpiration: '1h',
      refreshTokenExpiration: '7d',
      cookieSecure: process.env.NODE_ENV === 'production',
    }),

    // ForgeBase API module
    ForgeApiModule.forRoot({
      prefix: '/api',
      services: {
        db: {
          provider: 'sqlite',
          config: {
            filename: './mydb.sqlite',
            enforceRls: true,
          },
        },
      },
    }),

    // Your other modules...
  ],
})
export class AppModule {}
```

### User Context Guard

NestJS uses guards to extract the user context:

```typescript [ts]
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { AuthService } from '@the-forgebase/auth/adapters/nest';

@Injectable()
export class UserContextGuard implements CanActivate {
  constructor(private authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // Extract user context from request
    const session = await this.authService.getUserContext(request);

    if (session) {
      request.userContext = session.userContext;
      request.isAdmin = session.isAdmin;
      request.isSystem = session.isSystem;
    }

    return true;
  }
}
```

## Web Standard Integration

### Setting Up Authentication

To integrate authentication with the Web Standard adapter, you can use the ForgeBase Auth package:

```typescript [ts]
import { forgeApi } from '@the-forgebase/api';
import { WebAdapter } from '@the-forgebase/api/adapters';
import { createAuthContainer } from '@the-forgebase/auth';
import { webAuthApi, userContextMiddleware } from '@the-forgebase/auth/adapters/web';

// Create auth container
const container = createAuthContainer({
  // Your auth dependencies
});

// Create auth config
const config = {
  jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
  cookieSecret: process.env.COOKIE_SECRET || 'your-cookie-secret',
  tokenExpiration: '1h',
  refreshTokenExpiration: '7d',
  cookieSecure: process.env.NODE_ENV === 'production',
};

// Create auth API
const authApi = webAuthApi({
  container,
  config,
  cors: {
    enabled: true,
    corsOptions: {
      origin: ['http://localhost:5173'],
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
    },
  },
});

// Initialize ForgeBase API
const api = forgeApi({
  prefix: '/api',
  services: {
    db: {
      provider: 'sqlite',
      config: {
        filename: './mydb.sqlite',
        enforceRls: true,
      },
    },
  },
});

// Handle requests
async function handleRequest(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);

    // Handle auth requests
    if (url.pathname.startsWith('/auth')) {
      return await authApi.handleRequest(request);
    }

    // Handle API requests
    if (url.pathname.startsWith('/api')) {
      // Extract user context
      await userContextMiddleware(request, container.cradle.authManager, config, container.cradle.adminManager);

      const adapter = new WebAdapter(request);
      const result = await api.handle(adapter);

      if (result) {
        const { context } = result;
        return new Response(JSON.stringify(context.res.body), {
          status: context.res.status,
          headers: {
            'Content-Type': 'application/json',
            ...context.res.headers,
          },
        });
      }
    }

    // Handle other requests
    return new Response('Not Found', { status: 404 });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Export for serverless environments
export default {
  fetch: handleRequest,
};
```

## Row-Level Security

Once you've set up authentication, you can use row-level security to control access to your data:

```typescript [ts]
// Set permissions for a table
await api.getDatabaseService().setPermissions('posts', {
  operations: {
    SELECT: [
      // Allow anyone to read published posts
      {
        allow: 'customSql',
        customSql: 'SELECT 1 FROM posts WHERE is_published = true',
      },
      // Allow users to read their own posts
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    INSERT: [
      // Allow authenticated users to create posts
      {
        allow: 'auth',
      },
    ],
    UPDATE: [
      // Allow users to update their own posts
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      // Allow admins to update any post
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
    DELETE: [
      // Allow users to delete their own posts
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      // Allow admins to delete any post
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
  },
});
```

## Next Steps

- [Explore the REST API endpoints](/api/rest-api-reference)
- [Manage your database schema](/api/schema-management)
- [Perform database operations](/api/database-operations)
- [Create custom routes](/api/custom-routes)
