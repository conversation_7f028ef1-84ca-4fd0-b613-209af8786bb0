---
title: REST API Reference
description: Comprehensive reference for the ForgeBase API REST endpoints
icon: 'lucide:book-open'
---

ForgeBase API automatically creates RESTful endpoints for your database. This page provides a comprehensive reference for all available endpoints.

## Base URL

All API endpoints are prefixed with the value you specified in the `prefix` configuration option. By default, this is `/api`.

## Schema Endpoints

These endpoints allow you to manage your database schema.

### Get Database Schema

```http [http]
GET /api/db/schema
```

Returns the complete database schema, including all tables, columns, and relationships.

### Get Tables

```http [http]
GET /api/db/schema/tables
```

Returns a list of all tables in the database.

### Get Table Schema

```http [http]
GET /api/db/schema/tables/:tableName
```

Returns the schema for a specific table, including all columns and relationships.

### Create Table

```http [http]
POST /api/db/schema
Content-Type: application/json

{
  "tableName": "users",
  "columns": [
    { "name": "id", "type": "increments", "primary": true },
    { "name": "name", "type": "string" },
    { "name": "email", "type": "string", "unique": true }
  ]
}
```

Creates a new table with the specified columns.

### Add Column

```http [http]
POST /api/db/schema/column
Content-Type: application/json

{
  "tableName": "users",
  "columns": [
    { "name": "phone", "type": "string", "nullable": true }
  ]
}
```

Adds one or more columns to an existing table.

### Drop Column

```http [http]
DELETE /api/db/schema/column
Content-Type: application/json

{
  "tableName": "users",
  "column": "phone"
}
```

Removes a column from an existing table.

### Add Foreign Key

```http [http]
POST /api/db/schema/foreign_key
Content-Type: application/json

{
  "tableName": "posts",
  "foreignKey": {
    "column": "author_id",
    "references": {
      "table": "users",
      "column": "id"
    }
  }
}
```

Adds a foreign key constraint to a table.

### Drop Foreign Key

```http [http]
DELETE /api/db/schema/foreign_key
Content-Type: application/json

{
  "tableName": "posts",
  "column": "author_id"
}
```

Removes a foreign key constraint from a table.

### Delete Table

```http [http]
DELETE /api/db/schema/tables/:tableName
```

Deletes a table from the database.

## Data Endpoints

These endpoints allow you to perform CRUD operations on your data.

### Create Record

```http [http]
POST /api/db/create/:tableName
Content-Type: application/json

{
  "data": {
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

Creates a new record in the specified table.

### Query Records

```http [http]
POST /api/db/query/:tableName
Content-Type: application/json

{
  "query": {
    "select": ["id", "name", "email"],
    "filter": { "name": "John" },
    "limit": 10,
    "offset": 0,
    "orderBy": [{ "column": "name", "order": "asc" }]
  }
}
```

Queries records from the specified table with filtering, pagination, and sorting.

### Get Record by ID

```http [http]
GET /api/db/get/:tableName/:id
```

Retrieves a specific record by its ID.

### Update Record

```http [http]
PUT /api/db/update/:tableName/:id
Content-Type: application/json

{
  "data": {
    "name": "John Smith",
    "email": "<EMAIL>"
  }
}
```

Updates a specific record by its ID.

### Advanced Update

```http [http]
POST /api/db/update/:tableName
Content-Type: application/json

{
  "query": {
    "filter": { "status": "inactive" }
  },
  "data": {
    "status": "active"
  }
}
```

Updates multiple records that match the filter criteria.

### Delete Record

```http [http]
DELETE /api/db/del/:tableName/:id
```

Deletes a specific record by its ID.

### Advanced Delete

```http [http]
POST /api/db/del/:tableName
Content-Type: application/json

{
  "query": {
    "filter": { "status": "inactive" }
  }
}
```

Deletes multiple records that match the filter criteria.

## Permission Endpoints

These endpoints allow you to manage row-level security permissions.

### Get Table Permissions

```http [http]
GET /api/db/permissions/:tableName
```

Returns the permissions for a specific table.

### Set Table Permissions

```http [http]
POST /api/db/permissions/:tableName
Content-Type: application/json

{
  "operations": {
    "SELECT": [
      { "allow": "public" }
    ],
    "INSERT": [
      { "allow": "auth" }
    ],
    "UPDATE": [
      { "allow": "auth",
        "fieldCheck": {
          "field": "user_id",
          "operator": "===",
          "valueType": "userContext",
          "value": "userId"
        }
      }
    ],
    "DELETE": [
      { "allow": "role", "roles": ["admin"] }
    ]
  }
}
```

Sets the permissions for a specific table.

### Delete Table Permissions

```http [http]
DELETE /api/db/permissions/:tableName
```

Removes all permissions for a specific table.

## Query Parameters

Many endpoints support query parameters for filtering, pagination, and sorting:

### Filtering

You can filter records using the `filter` parameter:

```http [http]
GET /api/db/query/users?filter={"name":"John"}
```

### Pagination

You can paginate results using the `limit` and `offset` parameters:

```http [http]
GET /api/db/query/users?limit=10&offset=0
```

### Sorting

You can sort results using the `orderBy` parameter:

```http [http]
GET /api/db/query/users?orderBy=[{"column":"name","order":"asc"}]
```

### Column Selection

You can select specific columns using the `select` parameter:

```http [http]
GET /api/db/query/users?select=["id","name","email"]
```

## Authentication

All endpoints that modify data or schema require authentication, unless you've configured the API to allow public access.

The authentication method depends on your framework integration:

- **Express**: Uses session cookies or JWT tokens in the Authorization header
- **NestJS**: Uses the NestJS authentication system
- **Hono**: Uses cookies or JWT tokens in the Authorization header

## Error Handling

All endpoints return standard HTTP status codes:

- **200 OK**: The request was successful
- **201 Created**: The resource was created successfully
- **400 Bad Request**: The request was invalid
- **401 Unauthorized**: Authentication is required
- **403 Forbidden**: The user doesn't have permission to perform the action
- **404 Not Found**: The resource wasn't found
- **500 Internal Server Error**: An error occurred on the server

Error responses include a JSON body with an error message:

```json [json]
{
  "error": "Bad Request",
  "message": "Invalid data format"
}
```

## Next Steps

- [Learn about authentication](/api/authentication)
- [Explore database operations](/api/database-operations)
- [Manage your database schema](/api/schema-management)
- [Create custom routes](/api/custom-routes)
