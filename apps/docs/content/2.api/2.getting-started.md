---
title: Getting Started
description: Learn how to set up and use the ForgeBase API package
icon: 'lucide:play'
---

This guide will help you get started with the ForgeBase API package, showing you how to set it up and integrate it with your application.

## Installation

First, install the ForgeBase API package:

:pm-install{name="@the-forgebase/api"}

You'll also need to install the database package:

:pm-install{name="@the-forgebase/database"}

If you plan to use storage features, install the storage package as well:

:pm-install{name="@the-forgebase/storage"}

## Basic Setup

The first step in using ForgeBase API is to set up a database connection:

```typescript [ts]
import knex from 'knex';

// Create a Knex instance for your database
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});
```

## Framework Integration Examples

Below are examples of integrating ForgeBase API with various frameworks. For more detailed examples, see the [Framework Integration](/api/framework-integration) page.

### Integrating with Express

Here's how to integrate ForgeBase API with an Express application:

```typescript [ts]
import express from 'express';
import cors from 'cors';
import { createExpressHandler } from '@the-forgebase/api/core/express';
import knex from 'knex';

// Create a Knex instance
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Create Express app
const app = express();

// Configure middleware
app.use(
  cors({
    origin: 'http://localhost:5173', // Your frontend URL
    credentials: true,
  }),
);
app.use(express.json());

// Create Express handler
const expressHandler = createExpressHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
  },
  fgConfig: {
    prefix: '/api',
    services: {
      db: {
        provider: 'sqlite',
        config: {
          db: knexInstance,
          enforceRls: true,
        },
      },
    },
  },
});

// Mount the ForgeBase API
app.use('/api', expressHandler.getRouter());

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

### Integrating with Hono

Here's how to integrate ForgeBase API with a Hono application:

```typescript [ts]
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { createIttyHandler } from '@the-forgebase/api/core/web';
import knex from 'knex';

// Create a Knex instance
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Create Hono app
const app = new Hono();

// Configure middleware
app.use(
  '*',
  cors({
    origin: 'http://localhost:5173', // Your frontend URL
    credentials: true,
  }),
);

// Create web handler
const webHandler = createIttyHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
    corsEnabled: true,
  },
  fgConfig: {
    prefix: '/api',
    services: {
      db: {
        provider: 'sqlite',
        config: {
          db: knexInstance,
          enforceRls: true,
        },
      },
    },
  },
});

// Apply ForgeBase API to specific routes
app.all('/api/*', async (c) => {
  try {
    return await webHandler.handleRequest(c.req.raw);
  } catch (error) {
    return c.json({ error: error.message }, 500);
  }
});

// Export for serverless environments
export default {
  port: 3000,
  fetch: app.fetch,
};
```

### Integrating with NestJS

ForgeBase API provides dedicated modules for NestJS integration:

```typescript [ts]
import { Module } from '@nestjs/common';
import { ForgeApiModule } from '@the-forgebase/api/core/nest';

@Module({
  imports: [
    ForgeApiModule.forRoot({
      prefix: '/api',
      services: {
        db: {
          provider: 'sqlite',
          config: {
            filename: './mydb.sqlite',
            enforceRls: true,
          },
        },
      },
    }),
    // Your other modules...
  ],
})
export class AppModule {}
```

For more advanced scenarios, you can use the `ForgeApiWithChildModule`:

```typescript [ts]
import { Module } from '@nestjs/common';
import { ForgeApiWithChildModule } from '@the-forgebase/api/core/nest';

// Root module with global configuration
@Module({
  imports: [
    ForgeApiWithChildModule.forRoot({
      prefix: '/api',
      services: {
        db: {
          provider: 'sqlite',
          config: {
            filename: './mydb.sqlite',
            enforceRls: true,
          },
        },
      },
    }),
    // Your other modules...
  ],
})
export class AppModule {}

// Feature module with its own configuration
@Module({
  imports: [
    ForgeApiWithChildModule.forChild({
      prefix: '/feature-api',
      services: {
        db: {
          provider: 'sqlite',
          config: {
            filename: './feature-database.sqlite',
            enforceRls: false,
          },
        },
      },
    }),
  ],
})
export class FeatureModule {}
```

### Integrating with Next.js

ForgeBase API can be easily integrated with Next.js API routes:

```typescript [ts]
// app/api/[[...route]]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createIttyHandler } from '@the-forgebase/api/core/web';
import knex from 'knex';

// Create a Knex instance (you might want to use a singleton pattern in a real app)
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Create web handler
const webHandler = createIttyHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
    corsEnabled: true,
  },
  fgConfig: {
    prefix: '/api',
    services: {
      db: {
        provider: 'sqlite',
        config: {
          db: knexInstance,
          enforceRls: true,
        },
      },
    },
  },
});

// Handle API requests
export async function GET(request: NextRequest) {
  return handleApiRequest(request);
}

export async function POST(request: NextRequest) {
  return handleApiRequest(request);
}

export async function PUT(request: NextRequest) {
  return handleApiRequest(request);
}

export async function DELETE(request: NextRequest) {
  return handleApiRequest(request);
}

async function handleApiRequest(request: NextRequest) {
  try {
    // Use the web handler to process the request
    const response = await webHandler.handleRequest(request);
    return response;
  } catch (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

## Using the API

Once you've set up the ForgeBase API, you can start using it to interact with your database:

### Accessing the Database Service

```typescript [ts]
// Get the database service from your handler
const db = expressHandler.getDatabaseService(); // or webHandler.getDatabaseService();

// Create a new record
const id = await db.insert(
  'users',
  {
    tableName: 'users',
    data: {
      name: 'John Doe',
      email: '<EMAIL>',
    },
  },
  userContext, // Optional user context for row-level security
);

// Query records
const users = await db.query(
  'users',
  {
    select: ['id', 'name', 'email'],
    filter: { name: 'John' },
    limit: 10,
    offset: 0,
  },
  userContext,
);

// Update a record
await db.update(
  'users',
  {
    tableName: 'users',
    id: 1,
    data: {
      name: 'John Smith',
    },
  },
  userContext,
);

// Delete a record
await db.delete('users', 1, userContext);
```

## Next Steps

Now that you've set up the ForgeBase API, you can:

- [Explore framework integrations](/api/framework-integration)
- [Explore the REST API endpoints](/api/rest-api-reference)
- [Implement authentication](/api/authentication)
- [Manage your database schema](/api/schema-management)
- [Perform database operations](/api/database-operations)
