---
title: Database Operations
description: Perform database operations through the ForgeBase API
icon: 'lucide:database'
---

ForgeBase API provides a complete interface to ForgeBase Database, allowing you to perform various database operations. This page explains how to use the API to interact with your database.

## Accessing the Database Service

You can access the database service directly from the API:

```typescript [ts]
// Get the database service
const db = api.getDatabaseService();
```

## Creating Records

### Using the API Directly

```typescript [ts]
// Create a new record
const id = await db.insert(
  'users',
  {
    tableName: 'users',
    data: {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
  },
  userContext, // Optional user context for row-level security
);
```

### Using REST Endpoints

```http [http]
POST /api/db/create/users
Content-Type: application/json

{
  "data": {
    "name": "<PERSON>",
    "email": "<EMAIL>"
  }
}
```

## Querying Records

### Using the API Directly

```typescript [ts]
// Query records
const users = await db.query(
  'users',
  {
    select: ['id', 'name', 'email'],
    filter: { name: '<PERSON>' },
    limit: 10,
    offset: 0,
    orderBy: [{ column: 'name', order: 'asc' }],
  },
  userContext,
);
```

### Using REST Endpoints

```http [http]
POST /api/db/query/users
Content-Type: application/json

{
  "query": {
    "select": ["id", "name", "email"],
    "filter": { "name": "John" },
    "limit": 10,
    "offset": 0,
    "orderBy": [{ "column": "name", "order": "asc" }]
  }
}
```

## Updating Records

### Using the API Directly

```typescript [ts]
// Update a record by ID
await db.update(
  'users',
  {
    tableName: 'users',
    id: 1,
    data: {
      name: 'John Smith',
    },
  },
  userContext,
);

// Advanced update with filter
await db.advanceUpdate(
  {
    tableName: 'users',
    query: {
      filter: { status: 'inactive' },
    },
    data: {
      status: 'active',
    },
  },
  userContext,
);
```

### Using REST Endpoints

```http [http]
PUT /api/db/update/users/1
Content-Type: application/json

{
  "data": {
    "name": "John Smith"
  }
}
```

```http [http]
POST /api/db/update/users
Content-Type: application/json

{
  "query": {
    "filter": { "status": "inactive" }
  },
  "data": {
    "status": "active"
  }
}
```

## Deleting Records

### Using the API Directly

```typescript [ts]
// Delete a record by ID
await db.delete('users', 1, userContext);

// Advanced delete with filter
await db.advanceDelete(
  {
    tableName: 'users',
    query: {
      filter: { status: 'inactive' },
    },
  },
  userContext,
);
```

### Using REST Endpoints

```http [http]
DELETE /api/db/del/users/1
```

```http [http]
POST /api/db/del/users
Content-Type: application/json

{
  "query": {
    "filter": { "status": "inactive" }
  }
}
```

## Filtering

You can filter records using various operators:

```typescript [ts]
// Equal
const users = await db.query(
  'users',
  {
    filter: { name: 'John' },
  },
  userContext,
);

// Not equal
const users = await db.query(
  'users',
  {
    filter: { name: { $ne: 'John' } },
  },
  userContext,
);

// Greater than
const users = await db.query(
  'users',
  {
    filter: { age: { $gt: 18 } },
  },
  userContext,
);

// Less than
const users = await db.query(
  'users',
  {
    filter: { age: { $lt: 65 } },
  },
  userContext,
);

// In array
const users = await db.query(
  'users',
  {
    filter: { status: { $in: ['active', 'pending'] } },
  },
  userContext,
);

// Like (pattern matching)
const users = await db.query(
  'users',
  {
    filter: { name: { $like: '%John%' } },
  },
  userContext,
);

// Complex conditions
const users = await db.query(
  'users',
  {
    filter: {
      $and: [{ age: { $gt: 18 } }, { age: { $lt: 65 } }, { $or: [{ status: 'active' }, { role: 'admin' }] }],
    },
  },
  userContext,
);
```

## Pagination

You can paginate results using the `limit` and `offset` parameters:

```typescript [ts]
// Get the first page (10 records)
const page1 = await db.query(
  'users',
  {
    limit: 10,
    offset: 0,
  },
  userContext,
);

// Get the second page (10 records)
const page2 = await db.query(
  'users',
  {
    limit: 10,
    offset: 10,
  },
  userContext,
);
```

## Sorting

You can sort results using the `orderBy` parameter:

```typescript [ts]
// Sort by a single column
const users = await db.query(
  'users',
  {
    orderBy: [{ column: 'name', order: 'asc' }],
  },
  userContext,
);

// Sort by multiple columns
const users = await db.query(
  'users',
  {
    orderBy: [
      { column: 'role', order: 'asc' },
      { column: 'name', order: 'asc' },
    ],
  },
  userContext,
);
```

## Selecting Columns

You can select specific columns using the `select` parameter:

```typescript [ts]
// Select specific columns
const users = await db.query(
  'users',
  {
    select: ['id', 'name', 'email'],
  },
  userContext,
);

// Select all columns
const users = await db.query(
  'users',
  {
    select: ['*'],
  },
  userContext,
);
```

## Transactions

You can perform multiple operations in a transaction:

```typescript [ts]
// Start a transaction
await db.transaction(async (trx) => {
  // Create a user
  const userId = await db.insert(
    'users',
    {
      tableName: 'users',
      data: {
        name: 'John Doe',
        email: '<EMAIL>',
      },
    },
    userContext,
    false, // Not a system operation
    trx, // Pass the transaction
  );

  // Create a post for the user
  await db.insert(
    'posts',
    {
      tableName: 'posts',
      data: {
        title: 'My First Post',
        content: 'Hello, world!',
        author_id: userId,
      },
    },
    userContext,
    false, // Not a system operation
    trx, // Pass the transaction
  );
});
```

## Row-Level Security

ForgeBase API automatically enforces row-level security based on the user context:

```typescript [ts]
// Set permissions for a table
await db.setPermissions('posts', {
  operations: {
    SELECT: [
      // Allow anyone to read published posts
      {
        allow: 'customSql',
        customSql: 'SELECT 1 FROM posts WHERE is_published = true',
      },
      // Allow users to read their own posts
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    INSERT: [
      // Allow authenticated users to create posts
      {
        allow: 'auth',
      },
    ],
    UPDATE: [
      // Allow users to update their own posts
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    DELETE: [
      // Allow users to delete their own posts
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
  },
});

// Query posts (will only return posts the user has permission to see)
const posts = await db.query('posts', {}, userContext);
```

## System Operations

You can bypass row-level security for administrative operations:

```typescript [ts]
// Create a record as a system operation (bypasses RLS)
const id = await db.insert(
  'users',
  {
    tableName: 'users',
    data: {
      name: 'John Doe',
      email: '<EMAIL>',
    },
  },
  userContext,
  true, // System operation
);
```

## Error Handling

ForgeBase API throws specific errors for different scenarios:

```typescript [ts]
try {
  const users = await db.query('users', {}, userContext);
} catch (error) {
  if (error instanceof AuthenticationRequiredError) {
    // User is not authenticated
    console.error('Authentication required');
  } else if (error instanceof PermissionDeniedError) {
    // User doesn't have permission
    console.error('Permission denied');
  } else if (error instanceof ExcludedTableError) {
    // Table doesn't exist or is excluded
    console.error('Table does not exist');
  } else {
    // Other error
    console.error('Error:', error.message);
  }
}
```

## Next Steps

- [Explore the REST API endpoints](/api/rest-api-reference)
- [Implement authentication](/api/authentication)
- [Manage your database schema](/api/schema-management)
- [Create custom routes](/api/custom-routes)
