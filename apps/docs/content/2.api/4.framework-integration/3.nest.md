---
title: NestJS Integration
description: Integrate ForgeBase API with NestJS
icon: 'lucide:layers'
---

NestJS is a progressive Node.js framework, and ForgeBase API provides dedicated modules for it through the `@the-forgebase/api/core/nest` module.

## Basic Integration

```typescript [ts]
import { Module } from '@nestjs/common';
import { ForgeApiModule } from '@the-forgebase/api/core/nest';

@Module({
  imports: [
    ForgeApiModule.forRoot({
      prefix: '/api',
      services: {
        db: {
          provider: 'sqlite',
          config: {
            filename: './mydb.sqlite',
            enforceRls: true,
          },
        },
      },
    }),
    // Your other modules...
  ],
})
export class AppModule {}
```

## Child Module Support

For more advanced scenarios, you can use the `ForgeApiWithChildModule`:

```typescript [ts]
import { Module } from '@nestjs/common';
import { ForgeApiWithChildModule } from '@the-forgebase/api/core/nest';

// Root module with global configuration
@Module({
  imports: [
    ForgeApiWithChildModule.forRoot({
      prefix: '/api',
      services: {
        db: {
          provider: 'sqlite',
          config: {
            filename: './mydb.sqlite',
            enforceRls: true,
          },
        },
      },
    }),
    // Your other modules...
  ],
})
export class AppModule {}

// Feature module with its own configuration
@Module({
  imports: [
    ForgeApiWithChildModule.forChild({
      prefix: '/feature-api',
      services: {
        db: {
          provider: 'sqlite',
          config: {
            filename: './feature-database.sqlite',
            enforceRls: false,
          },
        },
      },
    }),
  ],
})
export class FeatureModule {}
```

## Authentication with NestJS

To integrate authentication with NestJS, you can use the ForgeBase Auth package:

```typescript [ts]
import { Module } from '@nestjs/common';
import { ForgeApiModule } from '@the-forgebase/api/core/nest';
import { NestAuthModule } from '@the-forgebase/auth/adapters/nest';

@Module({
  imports: [
    // ForgeBase Auth module
    NestAuthModule.forRoot({
      jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
      cookieSecret: process.env.COOKIE_SECRET || 'your-cookie-secret',
      tokenExpiration: '1h',
      refreshTokenExpiration: '7d',
      cookieSecure: process.env.NODE_ENV === 'production',
    }),

    // ForgeBase API module
    ForgeApiModule.forRoot({
      prefix: '/api',
      services: {
        db: {
          provider: 'sqlite',
          config: {
            filename: './mydb.sqlite',
            enforceRls: true,
          },
        },
      },
    }),

    // Your other modules...
  ],
})
export class AppModule {}
```

## Accessing Services

You can inject and use the ForgeBase services in your NestJS controllers and services:

```typescript [ts]
import { Controller, Get, Inject } from '@nestjs/common';
import { DatabaseService } from '@the-forgebase/api/core/nest';

@Controller('users')
export class UsersController {
  constructor(@Inject(DatabaseService) private readonly db: DatabaseService) {}

  @Get()
  async findAll() {
    return this.db.query(
      'users',
      {
        select: ['id', 'name', 'email'],
      },
      { userId: 1, role: 'admin' },
    );
  }
}
```

## Next Steps

- [Explore the Express integration](/api/framework-integration/express)
- [Learn about the Web integration](/api/framework-integration/web)
- [Discover the Ultimate Express integration](/api/framework-integration/ultimate-express)
