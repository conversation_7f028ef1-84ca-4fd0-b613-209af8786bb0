---
title: Ultimate Express Integration
description: Integrate ForgeBase API with Ultimate Express
icon: 'lucide:zap'
---

Ultimate Express is an enhanced version of Express with additional features, and ForgeBase API provides integration with it through the `@the-forgebase/api/core/ultimate-express` module.

## Basic Integration

```typescript [ts]
import express from 'express';
import cors from 'cors';
import { createUltimateExpressHandler } from '@the-forgebase/api/core/ultimate-express';
import knex from 'knex';

// Create a Knex instance
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Create Express app
const app = express();

// Configure middleware
app.use(
  cors({
    origin: 'http://localhost:5173', // Your frontend URL
    credentials: true,
  }),
);
app.use(express.json());

// Create Ultimate Express handler
const ultimateExpressHandler = createUltimateExpressHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
  },
  fgConfig: {
    prefix: '/api',
    services: {
      db: {
        provider: 'sqlite',
        config: {
          db: knexInstance,
          enforceRls: true,
        },
      },
    },
  },
});

// Mount the ForgeBase API
app.use('/api', ultimateExpressHandler.getRouter());

// Add your custom routes
app.get('/hello', (req, res) => {
  res.json({ message: 'Hello, World!' });
});

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

## Advanced Features

Ultimate Express provides additional features beyond standard Express:

### Enhanced Error Handling

```typescript [ts]
// Create Ultimate Express handler with enhanced error handling
const ultimateExpressHandler = createUltimateExpressHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
    errorHandler: (err, req, res, next) => {
      console.error('API Error:', err);
      res.status(500).json({
        error: err.message,
        code: err.code || 'INTERNAL_SERVER_ERROR',
        stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
      });
    },
  },
  fgConfig: {
    // Configuration...
  },
});
```

### Authentication Integration

```typescript [ts]
import { createUltimateExpressHandler } from '@the-forgebase/api/core/ultimate-express';
import { createExpressAuthClient, initializeExpressAuth } from '@the-forgebase/auth/adapters/express';

// Configure auth
const { container, config } = createExpressAuthClient({
  config: {
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    cookieSecret: process.env.COOKIE_SECRET || 'your-cookie-secret',
    tokenExpiration: '1h',
    refreshTokenExpiration: '7d',
    cookieSecure: process.env.NODE_ENV === 'production',
  },
  deps: {
    // Your auth dependencies
  },
});

// Initialize auth
const { authRouter, adminRouter } = initializeExpressAuth(container.cradle.authManager, container.cradle.adminManager, config);

// Create Ultimate Express handler with auth integration
const ultimateExpressHandler = createUltimateExpressHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
    authManager: container.cradle.authManager,
  },
  fgConfig: {
    // Configuration...
  },
});

// Mount auth routes
app.use('/auth', authRouter);
app.use('/admin', adminRouter);

// Mount the ForgeBase API
app.use('/api', ultimateExpressHandler.getRouter());
```

## Direct Database Access

You can access the database service directly for custom operations:

```typescript [ts]
// Get the database service
const db = ultimateExpressHandler.getDatabaseService();

// Perform database operations
const users = await db.query(
  'users',
  {
    select: ['id', 'name', 'email'],
    filter: { role: 'admin' },
  },
  userContext,
);
```

## Next Steps

- [Explore the Express integration](/api/framework-integration/express)
- [Learn about the NestJS integration](/api/framework-integration/nest)
- [Discover the Web integration](/api/framework-integration/web)
