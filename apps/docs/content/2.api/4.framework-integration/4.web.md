---
title: Web Integration
description: Integrate ForgeBase API with Hono, Next.js, and other web standard frameworks
icon: 'lucide:globe'
---

ForgeBase API provides a web integration module through `@the-forgebase/api/core/web` that works with any framework using the standard Web API (Request/Response objects), including Hono and Next.js.

## Hono Integration

[Hono](https://hono.dev/) is a lightweight, edge-compatible web framework, and ForgeBase API provides integration with it:

```typescript [ts]
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { createIttyHandler } from '@the-forgebase/api/core/web';
import knex from 'knex';

// Create a Knex instance
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Create Hono app
const app = new Hono();

// Configure middleware
app.use(
  '*',
  cors({
    origin: 'http://localhost:5173', // Your frontend URL
    credentials: true,
  }),
);

// Create web handler
const webHandler = createIttyHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
    corsEnabled: true,
  },
  fgConfig: {
    prefix: '/api',
    services: {
      db: {
        provider: 'sqlite',
        config: {
          db: knexInstance,
          enforceRls: true,
        },
      },
    },
  },
});

// Apply ForgeBase API to specific routes
app.all('/api/*', async (c) => {
  try {
    return await webHandler.handleRequest(c.req.raw);
  } catch (error) {
    return c.json({ error: error.message }, 500);
  }
});

// Add your custom routes
app.get('/hello', (c) => c.json({ message: 'Hello, World!' }));

// Export for serverless environments
export default {
  port: 3000,
  fetch: app.fetch,
};
```

## Next.js Integration

[Next.js](https://nextjs.org/) is a popular React framework, and ForgeBase API can be easily integrated with it:

```typescript [ts]
// app/api/[[...route]]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createIttyHandler } from '@the-forgebase/api/core/web';
import knex from 'knex';

// Create a Knex instance (you might want to use a singleton pattern in a real app)
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Create web handler
const webHandler = createIttyHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
    corsEnabled: true,
  },
  fgConfig: {
    prefix: '/api',
    services: {
      db: {
        provider: 'sqlite',
        config: {
          db: knexInstance,
          enforceRls: true,
        },
      },
    },
  },
});

// Handle API requests
export async function GET(request: NextRequest) {
  return handleApiRequest(request);
}

export async function POST(request: NextRequest) {
  return handleApiRequest(request);
}

export async function PUT(request: NextRequest) {
  return handleApiRequest(request);
}

export async function DELETE(request: NextRequest) {
  return handleApiRequest(request);
}

async function handleApiRequest(request: NextRequest) {
  try {
    // Use the web handler to process the request
    const response = await webHandler.handleRequest(request);
    return response;
  } catch (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

## Web Standard Integration

ForgeBase API can work with any framework that uses the standard Web API:

```typescript [ts]
import { createIttyHandler } from '@the-forgebase/api/core/web';
import knex from 'knex';

// Create a Knex instance
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Create web handler
const webHandler = createIttyHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
    corsEnabled: true,
  },
  fgConfig: {
    prefix: '/api',
    services: {
      db: {
        provider: 'sqlite',
        config: {
          db: knexInstance,
          enforceRls: true,
        },
      },
    },
  },
});

// Handle requests
async function handleRequest(request: Request): Promise<Response> {
  try {
    // Check if the request is for the API
    const url = new URL(request.url);
    if (url.pathname.startsWith('/api')) {
      return await webHandler.handleRequest(request);
    }

    // Handle other requests
    return new Response('Not Found', { status: 404 });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Export for serverless environments
export default {
  fetch: handleRequest,
};
```

## Authentication with Web Frameworks

To integrate authentication with web frameworks, you can use the ForgeBase Auth package:

```typescript [ts]
import { createAuthContainer } from '@the-forgebase/auth';
import { webAuthApi } from '@the-forgebase/auth/adapters/web';

// Create auth container
const container = createAuthContainer({
  // Your auth dependencies
});

// Create auth API
const authApi = webAuthApi({
  container,
  config: {
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    cookieSecret: process.env.COOKIE_SECRET || 'your-cookie-secret',
    tokenExpiration: '1h',
    refreshTokenExpiration: '7d',
    cookieSecure: process.env.NODE_ENV === 'production',
  },
  cors: {
    enabled: true,
    corsOptions: {
      origin: ['http://localhost:5173'],
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
    },
  },
});

// Handle auth requests
app.all('/auth/*', async (c) => {
  try {
    const response = await authApi.handleRequest(c.req.raw);
    return new Response(response.body, {
      status: response.status,
      headers: response.headers,
    });
  } catch (error) {
    return c.json({ error: error.message }, 500);
  }
});
```

## Next Steps

- [Explore the Express integration](/api/framework-integration/express)
- [Learn about the NestJS integration](/api/framework-integration/nest)
- [Discover the Ultimate Express integration](/api/framework-integration/ultimate-express)
