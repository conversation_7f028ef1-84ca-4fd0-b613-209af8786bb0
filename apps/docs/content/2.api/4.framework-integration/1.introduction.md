---
title: Introduction
description: Integrate ForgeBase API with your preferred web framework
icon: 'lucide:door-open'
---

ForgeBase API is designed to work with any web framework through its integration system. This page explains how to integrate the API with various popular frameworks.

## Available Framework Integrations

ForgeBase API provides dedicated integrations for several popular frameworks:

- [Express](/api/framework-integration/express): Traditional Node.js web framework
- [NestJS](/api/framework-integration/nest): Progressive Node.js framework
- [Web](/api/framework-integration/web): Edge-compatible frameworks (Hono, Next.js, etc.)
- [Ultimate Express](/api/framework-integration/ultimate-express): Express with additional features

## Integration Approach

All framework integrations follow a similar pattern:

1. **Create a database connection** using Knex
2. **Initialize the framework-specific handler** with configuration
3. **Mount the handler** in your framework's routing system
4. **Access the database service** for direct database operations

## Next Steps

Choose your preferred framework from the list above to see detailed integration instructions.
