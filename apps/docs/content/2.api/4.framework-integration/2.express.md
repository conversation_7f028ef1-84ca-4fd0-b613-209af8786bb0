---
title: Express Integration
description: Integrate ForgeBase API with Express
icon: 'lucide:server'
---

Express is one of the most popular Node.js web frameworks, and ForgeBase API provides direct integration with it through the `@the-forgebase/api/core/express` module.

## Basic Integration

```typescript [ts]
import express from 'express';
import cors from 'cors';
import { createExpressHandler } from '@the-forgebase/api/core/express';
import knex from 'knex';

// Create a Knex instance
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Create Express app
const app = express();

// Configure middleware
app.use(
  cors({
    origin: 'http://localhost:5173', // Your frontend URL
    credentials: true,
  }),
);
app.use(express.json());

// Create Express handler
const expressHandler = createExpressHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
  },
  fgConfig: {
    prefix: '/api',
    services: {
      db: {
        provider: 'sqlite',
        config: {
          db: knexInstance,
          enforceRls: true,
        },
      },
    },
  },
});

// Mount the ForgeBase API
app.use('/api', expressHandler.getRouter());

// Add your custom routes
app.get('/hello', (req, res) => {
  res.json({ message: 'Hello, World!' });
});

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

## Authentication with Express

To integrate authentication with Express, you can use the ForgeBase Auth package:

```typescript [ts]
import express from 'express';
import { createExpressHandler } from '@the-forgebase/api/core/express';
import { createExpressAuthClient, initializeExpressAuth } from '@the-forgebase/auth/adapters/express';

// Create Express app
const app = express();

// Configure auth
const { container, config } = createExpressAuthClient({
  config: {
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    cookieSecret: process.env.COOKIE_SECRET || 'your-cookie-secret',
    tokenExpiration: '1h',
    refreshTokenExpiration: '7d',
    cookieSecure: process.env.NODE_ENV === 'production',
  },
  deps: {
    // Your auth dependencies
  },
});

// Initialize auth
const { authRouter, adminRouter } = initializeExpressAuth(container.cradle.authManager, container.cradle.adminManager, config);

// Mount auth routes
app.use('/auth', authRouter);
app.use('/admin', adminRouter);

// Create Express handler
const expressHandler = createExpressHandler({
  config: {
    enableSchemaEndpoints: true,
    enableDataEndpoints: true,
    enablePermissionEndpoints: true,
  },
  fgConfig: {
    prefix: '/api',
    services: {
      db: {
        provider: 'sqlite',
        config: {
          db: knexInstance,
          enforceRls: true,
        },
      },
    },
  },
});

// Mount the ForgeBase API
app.use('/api', expressHandler.getRouter());

// Start the server
app.listen(3000);
```

## Direct Database Access

You can access the database service directly for custom operations:

```typescript [ts]
// Get the database service
const db = expressHandler.getDatabaseService();

// Perform database operations
const users = await db.query(
  'users',
  {
    select: ['id', 'name', 'email'],
    filter: { role: 'admin' },
  },
  userContext,
);
```

## Next Steps

- [Explore the NestJS integration](/api/framework-integration/nest)
- [Learn about the Web integration](/api/framework-integration/web)
- [Discover the Ultimate Express integration](/api/framework-integration/ultimate-express)
