---
title: Schema Management
description: Manage your database schema through the ForgeBase API
icon: 'lucide:table'
---

ForgeBase API provides a complete interface for managing your database schema. This page explains how to use the API to create, modify, and delete tables and columns.

## Accessing the Database Service

You can access the database service directly from the API:

```typescript [ts]
// Get the database service
const db = api.getDatabaseService();
```

## Getting Schema Information

### Get Database Schema

```typescript [ts]
// Get the complete database schema
const schema = await db.getSchema();
```

### Get Tables

```typescript [ts]
// Get all tables
const tables = await db.getTables();
```

### Get Table Schema

```typescript [ts]
// Get schema for a specific table
const tableSchema = await db.getTableSchema('users');
```

## Creating Tables

### Using the API Directly

```typescript [ts]
// Create a new table
await db.createSchema('users', [
  { name: 'id', type: 'increments', primary: true },
  { name: 'name', type: 'string' },
  { name: 'email', type: 'string', unique: true },
  { name: 'age', type: 'integer', nullable: true },
  { name: 'created_at', type: 'timestamp', defaultToNow: true },
]);
```

### Using REST Endpoints

```http [http]
POST /api/db/schema
Content-Type: application/json

{
  "tableName": "users",
  "columns": [
    { "name": "id", "type": "increments", "primary": true },
    { "name": "name", "type": "string" },
    { "name": "email", "type": "string", "unique": true },
    { "name": "age", "type": "integer", "nullable": true },
    { "name": "created_at", "type": "timestamp", "defaultToNow": true }
  ]
}
```

## Column Types

ForgeBase API supports various column types:

| Type         | Description                             | Example                                                         |
| ------------ | --------------------------------------- | --------------------------------------------------------------- |
| `increments` | Auto-incrementing integer               | `{ name: 'id', type: 'increments', primary: true }`             |
| `integer`    | Integer number                          | `{ name: 'age', type: 'integer' }`                              |
| `bigInteger` | Big integer number                      | `{ name: 'views', type: 'bigInteger' }`                         |
| `string`     | String with optional length             | `{ name: 'name', type: 'string', length: 255 }`                 |
| `text`       | Long text                               | `{ name: 'content', type: 'text' }`                             |
| `float`      | Floating-point number                   | `{ name: 'price', type: 'float' }`                              |
| `decimal`    | Decimal number with precision and scale | `{ name: 'amount', type: 'decimal', precision: 8, scale: 2 }`   |
| `boolean`    | Boolean value                           | `{ name: 'is_active', type: 'boolean', defaultValue: true }`    |
| `date`       | Date without time                       | `{ name: 'birth_date', type: 'date' }`                          |
| `datetime`   | Date and time                           | `{ name: 'created_at', type: 'datetime' }`                      |
| `timestamp`  | Timestamp                               | `{ name: 'updated_at', type: 'timestamp', defaultToNow: true }` |
| `json`       | JSON data                               | `{ name: 'metadata', type: 'json' }`                            |
| `uuid`       | UUID                                    | `{ name: 'id', type: 'uuid', primary: true }`                   |
| `binary`     | Binary data                             | `{ name: 'image', type: 'binary' }`                             |

## Column Constraints

You can add various constraints to columns:

```typescript [ts]
// Primary key
{ name: 'id', type: 'increments', primary: true }

// Unique constraint
{ name: 'email', type: 'string', unique: true }

// Not null constraint
{ name: 'name', type: 'string', nullable: false }

// Default value
{ name: 'status', type: 'string', defaultValue: 'active' }

// Default to current timestamp
{ name: 'created_at', type: 'timestamp', defaultToNow: true }

// Foreign key
{
  name: 'author_id',
  type: 'integer',
  references: {
    table: 'users',
    column: 'id'
  }
}
```

## Modifying Tables

### Adding Columns

```typescript [ts]
// Add columns to an existing table
await db.addColumn('users', [
  { name: 'phone', type: 'string', nullable: true },
  { name: 'address', type: 'string', nullable: true },
]);
```

```http [http]
POST /api/db/schema/column
Content-Type: application/json

{
  "tableName": "users",
  "columns": [
    { "name": "phone", "type": "string", "nullable": true },
    { "name": "address", "type": "string", "nullable": true }
  ]
}
```

### Modifying Columns

```typescript [ts]
// Modify columns in an existing table
await db.modifyColumn('users', [{ name: 'phone', type: 'string', nullable: false }]);
```

```http [http]
PUT /api/db/schema/column
Content-Type: application/json

{
  "tableName": "users",
  "columns": [
    { "name": "phone", "type": "string", "nullable": false }
  ]
}
```

### Dropping Columns

```typescript [ts]
// Drop a column from an existing table
await db.dropColumn('users', 'phone');
```

```http [http]
DELETE /api/db/schema/column
Content-Type: application/json

{
  "tableName": "users",
  "column": "phone"
}
```

## Foreign Keys

### Adding Foreign Keys

```typescript [ts]
// Add a foreign key to a table
await db.addForeignKey('posts', {
  column: 'author_id',
  references: {
    table: 'users',
    column: 'id',
  },
});
```

```http [http]
POST /api/db/schema/foreign_key
Content-Type: application/json

{
  "tableName": "posts",
  "foreignKey": {
    "column": "author_id",
    "references": {
      "table": "users",
      "column": "id"
    }
  }
}
```

### Dropping Foreign Keys

```typescript [ts]
// Drop a foreign key from a table
await db.dropForeignKey('posts', 'author_id');
```

```http [http]
DELETE /api/db/schema/foreign_key
Content-Type: application/json

{
  "tableName": "posts",
  "column": "author_id"
}
```

## Deleting Tables

```typescript [ts]
// Delete a table
await db.deleteSchema('users');
```

```http [http]
DELETE /api/db/schema/tables/users
```

## Indexes

### Adding Indexes

```typescript [ts]
// Add an index to a table
await db.addIndex('users', {
  columns: ['name', 'email'],
  unique: true,
  name: 'idx_users_name_email',
});
```

```http [http]
POST /api/db/schema/index
Content-Type: application/json

{
  "tableName": "users",
  "index": {
    "columns": ["name", "email"],
    "unique": true,
    "name": "idx_users_name_email"
  }
}
```

### Dropping Indexes

```typescript [ts]
// Drop an index from a table
await db.dropIndex('users', 'idx_users_name_email');
```

```http [http]
DELETE /api/db/schema/index
Content-Type: application/json

{
  "tableName": "users",
  "indexName": "idx_users_name_email"
}
```

## Permissions

After creating a table, you should set permissions for it:

```typescript [ts]
// Set permissions for a table
await db.setPermissions('users', {
  operations: {
    SELECT: [{ allow: 'public' }],
    INSERT: [{ allow: 'auth' }],
    UPDATE: [
      {
        allow: 'auth',
        fieldCheck: {
          field: 'id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    DELETE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
  },
});
```

```http [http]
POST /api/db/permissions/users
Content-Type: application/json

{
  "operations": {
    "SELECT": [
      { "allow": "public" }
    ],
    "INSERT": [
      { "allow": "auth" }
    ],
    "UPDATE": [
      {
        "allow": "auth",
        "fieldCheck": {
          "field": "id",
          "operator": "===",
          "valueType": "userContext",
          "value": "userId"
        }
      }
    ],
    "DELETE": [
      {
        "allow": "role",
        "roles": ["admin"]
      }
    ]
  }
}
```

## Example: Creating a Blog Schema

Here's a complete example of creating a blog schema:

```typescript [ts]
// Create users table
await db.createSchema('users', [
  { name: 'id', type: 'increments', primary: true },
  { name: 'username', type: 'string', unique: true },
  { name: 'email', type: 'string', unique: true },
  { name: 'password_hash', type: 'string' },
  { name: 'role', type: 'string', defaultValue: 'user' },
  { name: 'created_at', type: 'timestamp', defaultToNow: true },
  { name: 'updated_at', type: 'timestamp', defaultToNow: true },
]);

// Set permissions for users table
await db.setPermissions('users', {
  operations: {
    SELECT: [
      {
        allow: 'public',
        fieldCheck: {
          field: 'id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      { allow: 'role', roles: ['admin'] },
    ],
    INSERT: [
      { allow: 'public' }, // Allow registration
    ],
    UPDATE: [
      {
        allow: 'auth',
        fieldCheck: {
          field: 'id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      { allow: 'role', roles: ['admin'] },
    ],
    DELETE: [{ allow: 'role', roles: ['admin'] }],
  },
});

// Create posts table
await db.createSchema('posts', [
  { name: 'id', type: 'increments', primary: true },
  { name: 'title', type: 'string' },
  { name: 'content', type: 'text' },
  { name: 'author_id', type: 'integer', references: { table: 'users', column: 'id' } },
  { name: 'is_published', type: 'boolean', defaultValue: false },
  { name: 'created_at', type: 'timestamp', defaultToNow: true },
  { name: 'updated_at', type: 'timestamp', defaultToNow: true },
]);

// Set permissions for posts table
await db.setPermissions('posts', {
  operations: {
    SELECT: [
      { allow: 'customSql', customSql: 'SELECT 1 FROM posts WHERE is_published = true' },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      { allow: 'role', roles: ['admin'] },
    ],
    INSERT: [{ allow: 'auth' }],
    UPDATE: [
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      { allow: 'role', roles: ['admin'] },
    ],
    DELETE: [
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      { allow: 'role', roles: ['admin'] },
    ],
  },
});

// Create comments table
await db.createSchema('comments', [
  { name: 'id', type: 'increments', primary: true },
  { name: 'content', type: 'text' },
  { name: 'post_id', type: 'integer', references: { table: 'posts', column: 'id' } },
  { name: 'author_id', type: 'integer', references: { table: 'users', column: 'id' } },
  { name: 'created_at', type: 'timestamp', defaultToNow: true },
  { name: 'updated_at', type: 'timestamp', defaultToNow: true },
]);

// Set permissions for comments table
await db.setPermissions('comments', {
  operations: {
    SELECT: [
      {
        allow: 'customSql',
        customSql: `
        SELECT 1 FROM comments
        JOIN posts ON comments.post_id = posts.id
        WHERE posts.is_published = true
      `,
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      { allow: 'role', roles: ['admin'] },
    ],
    INSERT: [{ allow: 'auth' }],
    UPDATE: [
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      { allow: 'role', roles: ['admin'] },
    ],
    DELETE: [
      {
        allow: 'auth',
        fieldCheck: {
          field: 'author_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      { allow: 'role', roles: ['admin'] },
    ],
  },
});
```

## Next Steps

- [Explore the REST API endpoints](/api/rest-api-reference)
- [Implement authentication](/api/authentication)
- [Perform database operations](/api/database-operations)
- [Create custom routes](/api/custom-routes)
