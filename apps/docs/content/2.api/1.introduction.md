---
title: Introduction
description: A flexible, framework-agnostic API library for building robust backend services with built-in database and storage capabilities
icon: 'lucide:door-open'
---

ForgeBase API is a flexible, framework-agnostic API library that provides a unified interface for handling HTTP requests, database operations, and file storage across various Node.js frameworks. It enables developers to easily integrate ForgeBase backend services into their applications by abstracting away the complexity of direct service interactions.

## Purpose

The API package serves as a bridge between your application and ForgeBase services, providing:

1. **Unified Interface**: A consistent way to interact with ForgeBase services regardless of the framework you're using
2. **Framework Integrations**: Built-in support for popular frameworks through core modules:
   - Express (`@the-forgebase/api/core/express`)
   - NestJS (`@the-forgebase/api/core/nest`)
   - Web (`@the-forgebase/api/core/web`) for Hono, Next.js, and other web standard frameworks
   - Ultimate Express (`@the-forgebase/api/core/ultimate-express`)
3. **Database Access**: Direct access to ForgeBase Database with full support for schema management, data operations, and row-level security
4. **Storage Integration**: Access to ForgeBase Storage for file management
5. **Authentication Support**: Integration with ForgeBase Auth for user authentication and authorization

## Core Features

### Framework Integration

ForgeBase API is designed to work with any web framework through its integration system:

- **Express**: Direct integration with Express applications via `@the-forgebase/api/core/express`
- **NestJS**: Dedicated modules (ForgeApiModule and ForgeApiWithChildModule) via `@the-forgebase/api/core/nest`
- **Web**: Edge-compatible integration for Hono, Next.js, and other web standard frameworks via `@the-forgebase/api/core/web`
- **Ultimate Express**: Enhanced Express integration with additional features via `@the-forgebase/api/core/ultimate-express`

### Database Operations

The API package provides a complete interface to ForgeBase Database:

- **Schema Management**: Create, modify, and delete tables and columns
- **Data Operations**: CRUD operations with support for filtering, pagination, and sorting
- **Row-Level Security**: Automatic enforcement of permissions based on user context
- **Real-time Updates**: Optional WebSocket support for real-time data changes

### REST API Endpoints

ForgeBase API automatically creates RESTful endpoints for your database:

- **Schema Endpoints**: Manage database schema through API calls
- **Data Endpoints**: Perform CRUD operations on your data
- **Permission Endpoints**: Manage row-level security permissions

### Request Handling

- **Unified Interface**: Consistent request/response handling across frameworks
- **Parameter Parsing**: Automatic parsing of route parameters, query strings, and request bodies
- **Content Negotiation**: Support for different content types
- **Error Handling**: Standardized error responses
- **Cookie Support**: Built-in cookie handling

### Security Features

- **Authentication Integration**: Works with ForgeBase Auth for user authentication
- **Row-Level Security**: Enforces database permissions based on user context
- **CORS Support**: Configurable CORS settings for cross-origin requests
- **Input Validation**: Validates and sanitizes input data
- **Admin Protection**: Special routes for administrative operations

## Installation

:pm-install{name="@the-forgebase/api"}

## Next Steps

- [Getting Started](/api/getting-started): Learn how to set up and use the API package
- [Framework Integration](/api/framework-integration): Integrate with your preferred framework
- [REST API Reference](/api/rest-api-reference): Explore the available REST API endpoints
- [Authentication](/api/authentication): Implement authentication with the API package
- [Database Operations](/api/database-operations): Perform database operations through the API
- [Schema Management](/api/schema-management): Manage your database schema

## License

[MIT](https://github.com/The-ForgeBase/forgebase-ts/blob/main/LICENSE)
