---
title: Query Builder
description: Learn how to build complex queries with the ForgeBase SDK
icon: 'lucide:code'
---

The ForgeBase SDK provides a powerful query builder that allows you to construct complex database queries using a fluent, chainable API. This guide covers the various methods and features of the query builder.

## Basic Query Building

### Table Selection

All queries start with selecting a table:

```typescript [ts]
// Start a query on the 'users' table
const query = db.table('users');
```

### Executing Queries

After building your query, you can execute it using the `query` method:

```typescript [ts]
// Build and execute a query
const users = await db.table('users').query();
```

## Selection Methods

### Selecting All Fields

By default, all fields are selected:

```typescript [ts]
// Select all fields
const users = await db.table('users').query();
```

### Selecting Specific Fields

You can select specific fields using the `select` method:

```typescript [ts]
// Select specific fields
const userEmails = await db.table('users').select('id', 'name', 'email').query();
```

## Filtering Methods

### Basic Where Clauses

The `where` method adds a condition to the query:

```typescript [ts]
// Simple equality condition
const activeUsers = await db.table('users').where('status', 'active').query();

// With comparison operator
const recentUsers = await db.table('users').where('created_at', '>', '2023-01-01').query();
```

### Combining Conditions

Multiple `where` calls are combined with AND logic:

```typescript [ts]
// Multiple conditions (AND)
const activeAdmins = await db.table('users').where('status', 'active').where('role', 'admin').query();
```

### OR Conditions

Use `orWhere` for OR logic:

```typescript [ts]
// OR conditions
const adminsOrModerators = await db.table('users').where('role', 'admin').orWhere('role', 'moderator').query();
```

### IN Operator

Use `whereIn` to check if a field's value is in a list:

```typescript [ts]
// IN operator
const specificUsers = await db.table('users').whereIn('id', [1, 2, 3]).query();
```

### NOT IN Operator

Use `whereNotIn` to check if a field's value is not in a list:

```typescript [ts]
// NOT IN operator
const otherUsers = await db.table('users').whereNotIn('id', [1, 2, 3]).query();
```

### NULL Checks

Use `whereNull` and `whereNotNull` to check for NULL values:

```typescript [ts]
// NULL check
const usersWithoutEmail = await db.table('users').whereNull('email').query();

// NOT NULL check
const usersWithEmail = await db.table('users').whereNotNull('email').query();
```

### BETWEEN Operator

Use `whereBetween` to check if a field's value is between two values:

```typescript [ts]
// BETWEEN operator
const mediumPricedProducts = await db.table('products').whereBetween('price', [10, 100]).query();
```

### NOT BETWEEN Operator

Use `whereNotBetween` to check if a field's value is not between two values:

```typescript [ts]
// NOT BETWEEN operator
const cheapOrExpensiveProducts = await db.table('products').whereNotBetween('price', [10, 100]).query();
```

### EXISTS Operator

Use `whereExists` to check if a subquery returns any results:

```typescript [ts]
// EXISTS operator
const usersWithOrders = await db
  .table('users')
  .whereExists((qb) => {
    qb.table('orders').whereRaw('orders.user_id = users.id');
  })
  .query();
```

### Complex Grouped Conditions

Use callback functions to create grouped conditions:

```typescript [ts]
// Grouped conditions
const complexQuery = await db
  .table('users')
  .where((qb) => {
    qb.where('role', 'admin').orWhere('role', 'moderator');
  })
  .where('status', 'active')
  .query();
```

## Sorting Methods

### Basic Sorting

Use `orderBy` to sort results:

```typescript [ts]
// Sort by a single field (ascending by default)
const sortedUsers = await db.table('users').orderBy('name').query();

// Sort by a single field (descending)
const reverseSortedUsers = await db.table('users').orderBy('name', 'desc').query();
```

### Multiple Sort Fields

Chain multiple `orderBy` calls for complex sorting:

```typescript [ts]
// Sort by multiple fields
const complexSortedUsers = await db.table('users').orderBy('role').orderBy('name', 'desc').query();
```

## Pagination Methods

### Limiting Results

Use `limit` to restrict the number of results:

```typescript [ts]
// Limit to 10 results
const limitedUsers = await db.table('users').limit(10).query();
```

### Offsetting Results

Use `offset` to skip a number of results:

```typescript [ts]
// Skip the first 10 results
const offsetUsers = await db.table('users').offset(10).query();
```

### Combining Limit and Offset

Combine `limit` and `offset` for pagination:

```typescript [ts]
// Get the second page of 10 results
const secondPage = await db.table('users').limit(10).offset(10).query();
```

## Aggregation Methods

### Counting Records

Use `count` to count records:

```typescript [ts]
// Count all users
const userCount = await db.table('users').count().query();

// Count with a condition
const activeUserCount = await db.table('users').where('status', 'active').count().query();
```

### Other Aggregations

The SDK supports various aggregation functions:

```typescript [ts]
// Sum a field
const totalOrders = await db.table('orders').sum('amount').query();

// Average a field
const averageOrderAmount = await db.table('orders').avg('amount').query();

// Find the minimum value
const cheapestProduct = await db.table('products').min('price').query();

// Find the maximum value
const mostExpensiveProduct = await db.table('products').max('price').query();
```

## Grouping Methods

### Group By

Use `groupBy` to group results:

```typescript [ts]
// Group users by role and count them
const usersByRole = await db.table('users').select('role').count('id', { as: 'user_count' }).groupBy('role').query();
```

### Having

Use `having` to filter grouped results:

```typescript [ts]
// Find roles with more than 5 users
const popularRoles = await db.table('users').select('role').count('id', { as: 'user_count' }).groupBy('role').having('user_count', '>', 5).query();
```

## Advanced Features

### Transactions

The SDK supports transactions for atomic operations:

```typescript [ts]
// Start a transaction
const trx = await db.transaction();

try {
  // Perform operations within the transaction
  const user = await trx.table('users').create({
    name: 'John Doe',
    email: '<EMAIL>',
  });

  await trx.table('profiles').create({
    user_id: user.id,
    bio: 'A new user',
  });

  // Commit the transaction
  await trx.commit();
} catch (error) {
  // Rollback on error
  await trx.rollback();
  throw error;
}
```

### Joins

The SDK supports various types of joins:

```typescript [ts]
// Inner join
const usersWithPosts = await db.table('users').join('posts', 'users.id', '=', 'posts.user_id').select('users.*', 'posts.title').query();

// Left join
const usersWithOptionalPosts = await db.table('users').leftJoin('posts', 'users.id', '=', 'posts.user_id').select('users.*', 'posts.title').query();

// Right join
const postsWithOptionalUsers = await db.table('posts').rightJoin('users', 'posts.user_id', '=', 'users.id').select('posts.*', 'users.name').query();
```

## Next Steps

Now that you understand how to use the query builder, you can explore more advanced features:

- [Authentication Integration](/client-packages/sdk/authentication): Integrate with ForgeBase Auth
- [Advanced Usage](/client-packages/sdk/advanced-usage): Explore advanced features and configurations
