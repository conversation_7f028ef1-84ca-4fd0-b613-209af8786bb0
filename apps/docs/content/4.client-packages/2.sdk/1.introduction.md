---
title: Introduction
description: Overview of the ForgeBase SDK package
icon: 'lucide:door-open'
---

The ForgeBase SDK is a powerful, type-safe TypeScript SDK for interacting with ForgeBase services, providing comprehensive database operations, real-time features, and advanced query capabilities.

## Core Features

### Type-Safe Query Builder

- Fluent API design for intuitive query construction
- Advanced filtering with complex conditions
- Support for joins and relations
- Aggregation functions (count, sum, avg, min, max)
- Transaction support
- Raw query support when needed
- Query optimization

### Database Operations

- Complete CRUD operations (Create, Read, Update, Delete)
- Batch operations for efficient data handling
- Pagination with limit and offset
- Sorting with multiple fields and directions
- Custom queries for complex requirements
- Schema validation
- Comprehensive error handling

### Security Features

- Input sanitization to prevent injection attacks
- Type validation for data integrity
- Error boundaries for graceful failure handling

### Integration Capabilities

- Seamless integration with ForgeBase Auth packages
- Support for custom authentication mechanisms
- Compatible with various frontend frameworks

## Installation

:pm-install{name="@the-forgebase/sdk"}

## Basic Usage

```typescript [ts]
import { DatabaseSDK } from '@the-forgebase/sdk/client';

// Initialize with your API URL
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  axiosConfig: {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  },
});

// Basic CRUD Operations
const users = await db.table('users').select('id', 'name', 'email').where('status', 'active').query();

// Create a new record
const newUser = await db.table('users').create({
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'user',
});

// Update a record
const updatedUser = await db.table('users').update(1, {
  name: 'John Smith',
});

// Delete a record
await db.table('users').delete(1);
```

## Next Steps

- [Getting Started](/client-packages/sdk/getting-started): Learn how to set up and use the SDK
- [Database Operations](/client-packages/sdk/database-operations): Perform database operations with the SDK
- [Query Builder](/client-packages/sdk/query-builder): Build complex queries with the fluent API
- [Authentication Integration](/client-packages/sdk/authentication): Integrate with ForgeBase Auth
- [Advanced Usage](/client-packages/sdk/advanced-usage): Explore advanced features and configurations
