---
title: Authentication Integration
description: Learn how to integrate the ForgeBase SDK with authentication
icon: 'lucide:lock'
---

The ForgeBase SDK can be integrated with ForgeBase Auth packages to make authenticated requests to your backend. This guide covers how to set up and use this integration.

## Overview

When you integrate the SDK with authentication, you get several benefits:

- **Automatic Authentication**: All requests include the authentication token
- **Token Refresh**: Expired tokens are automatically refreshed
- **Consistent Headers**: Authentication headers are consistently applied
- **Error Handling**: Authentication errors are properly handled

## Integration Methods

There are two main ways to integrate the SDK with authentication:

1. **Using the Auth Axios Instance**: Pass the axios instance from the auth package to the SDK
2. **Using Auth Interceptors**: Apply the auth interceptors to the SDK's axios instance

## Integration with Web Auth

### Installation

First, install both packages:

:pm-install{name="@the-forgebase/sdk @the-forgebase/web-auth"}

### Using the Auth Axios Instance

```typescript [ts]
import { DatabaseSDK } from '@the-forgebase/sdk/client';
import { ForgebaseWebAuth } from '@the-forgebase/web-auth';

// Initialize auth
const auth = new ForgebaseWebAuth({
  apiUrl: 'http://localhost:3000/api',
});

// Create a database SDK with the auth axios instance
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  axiosInstance: auth.api,
});

// Now all database requests will include authentication headers
// and benefit from automatic token refresh
const myData = await db.table('my_private_data').query();
```

### Using Auth Interceptors

```typescript [ts]
import { DatabaseSDK } from '@the-forgebase/sdk/client';
import { ForgebaseWebAuth } from '@the-forgebase/web-auth';

// Initialize auth
const auth = new ForgebaseWebAuth({
  apiUrl: 'http://localhost:3000/api',
});

// Get auth interceptors
const authInterceptors = auth.getAuthInterceptors();

// Create a database SDK with auth interceptors
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  authInterceptors,
});

// Now all database requests will include authentication headers
// and benefit from automatic token refresh
const myData = await db.table('my_private_data').query();
```

## Integration with React Native Auth

### Installation

First, install both packages:

:pm-install{name="@the-forgebase/sdk @the-forgebase/react-native-auth"}

### Using the Auth Axios Instance

```typescript [ts]
import { DatabaseSDK } from '@the-forgebase/sdk/client';
import { ForgebaseAuth, SecureStoreAdapter } from '@the-forgebase/react-native-auth';
import * as SecureStore from 'expo-secure-store';

// Create a secure store adapter
const secureStore = new SecureStoreAdapter(SecureStore);

// Initialize auth
const auth = new ForgebaseAuth({
  apiUrl: 'http://localhost:3000/api',
  storage: secureStore,
});

// Create a database SDK with the auth axios instance
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  axiosInstance: auth.api,
});

// Now all database requests will include authentication headers
// and benefit from automatic token refresh
const myData = await db.table('my_private_data').query();
```

### Using Auth Interceptors

```typescript [ts]
import { DatabaseSDK } from '@the-forgebase/sdk/client';
import { ForgebaseAuth, SecureStoreAdapter } from '@the-forgebase/react-native-auth';
import * as SecureStore from 'expo-secure-store';

// Create a secure store adapter
const secureStore = new SecureStoreAdapter(SecureStore);

// Initialize auth
const auth = new ForgebaseAuth({
  apiUrl: 'http://localhost:3000/api',
  storage: secureStore,
});

// Get auth interceptors
const authInterceptors = auth.getAuthInterceptors();

// Create a database SDK with auth interceptors
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  authInterceptors,
});

// Now all database requests will include authentication headers
// and benefit from automatic token refresh
const myData = await db.table('my_private_data').query();
```

## Using in React Components

### With Web Auth

```typescript [ts]
import React, { useEffect, useState } from 'react';
import { DatabaseSDK } from '@the-forgebase/sdk/client';
import { useAuth } from '@the-forgebase/web-auth/react';

function MyComponent() {
  const { getApi, isAuthenticated } = useAuth();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      if (isAuthenticated) {
        try {
          // Get the authenticated axios instance
          const api = getApi();

          // Create a database SDK with the auth axios instance
          const db = new DatabaseSDK({
            baseUrl: 'http://localhost:3000/api',
            axiosInstance: api,
          });

          // Fetch data with authentication
          const result = await db.table('my_data').query();
          setData(result.records || []);
        } catch (error) {
          console.error('Error fetching data:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchData();
  }, [isAuthenticated, getApi]);

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h1>My Data</h1>
      <ul>
        {data.map((item) => (
          <li key={item.id}>{item.name}</li>
        ))}
      </ul>
    </div>
  );
}
```

### With React Native Auth

```typescript [ts]
import React, { useEffect, useState } from 'react';
import { View, Text, FlatList } from 'react-native';
import { DatabaseSDK } from '@the-forgebase/sdk/client';
import { useAuth } from '@the-forgebase/react-native-auth/hooks';

function MyComponent() {
  const { getApi, isAuthenticated } = useAuth();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      if (isAuthenticated) {
        try {
          // Get the authenticated axios instance
          const api = getApi();

          // Create a database SDK with the auth axios instance
          const db = new DatabaseSDK({
            baseUrl: 'http://localhost:3000/api',
            axiosInstance: api,
          });

          // Fetch data with authentication
          const result = await db.table('my_data').query();
          setData(result.records || []);
        } catch (error) {
          console.error('Error fetching data:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchData();
  }, [isAuthenticated, getApi]);

  if (loading) return <Text>Loading...</Text>;

  return (
    <View>
      <Text>My Data</Text>
      <FlatList
        data={data}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => <Text>{item.name}</Text>}
      />
    </View>
  );
}
```

## Error Handling

When using the SDK with authentication, you should handle authentication errors appropriately:

```typescript [ts]
try {
  const data = await db.table('my_private_data').query();
  // Process data
} catch (error) {
  if (error.response?.status === 401) {
    // Unauthorized - handle authentication error
    console.error('Authentication error:', error.message);
    // Redirect to login or refresh token
  } else {
    // Other error
    console.error('Error fetching data:', error.message);
  }
}
```

## Next Steps

Now that you understand how to integrate the SDK with authentication, you can explore more advanced features:

- [Advanced Usage](/client-packages/sdk/advanced-usage): Explore advanced features and configurations
