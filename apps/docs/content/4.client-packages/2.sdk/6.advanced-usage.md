---
title: Advanced Usage
description: Explore advanced features and configurations of the ForgeBase SDK
icon: 'lucide:settings'
---

This guide covers advanced features and configurations of the ForgeBase SDK, including custom request configurations, error handling, and performance optimization.

## Custom Request Configurations

### Request-Specific Axios Configuration

You can provide custom axios configuration for individual requests:

```typescript [ts]
// Add custom headers to a specific request
const users = await db.table('users').query({
  headers: {
    'X-Custom-Header': 'value',
  },
});

// Set a different timeout for a specific request
const users = await db.table('users').query({
  timeout: 10000, // 10 seconds
});
```

### Base URL Configuration

You can configure the base URL when initializing the SDK:

```typescript [ts]
// Set the base URL
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
});

// Change the base URL later
db.setBaseUrl('https://production-api.example.com/api');
```

### Custom Axios Instance

You can provide your own axios instance with custom configuration:

```typescript [ts]
import axios from 'axios';

// Create a custom axios instance
const axiosInstance = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json',
    'X-Custom-Header': 'value',
  },
});

// Use the custom axios instance
const db = new DatabaseSDK({
  axiosInstance,
});
```

## Advanced Error Handling

### Custom Error Handling

You can implement custom error handling for all requests:

```typescript [ts]
import axios from 'axios';

// Create a custom axios instance with error handling
const axiosInstance = axios.create({
  baseURL: 'http://localhost:3000/api',
});

// Add a response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Server error:', error.response.data);
      console.error('Status:', error.response.status);

      // Custom error handling based on status code
      if (error.response.status === 429) {
        console.error('Rate limit exceeded. Please try again later.');
      } else if (error.response.status === 503) {
        console.error('Service unavailable. Please try again later.');
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Network error:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Request error:', error.message);
    }

    // You can transform the error or return a custom error
    return Promise.reject(error);
  },
);

// Use the custom axios instance
const db = new DatabaseSDK({
  axiosInstance,
});
```

### Retry Logic

You can implement retry logic for failed requests:

```typescript [ts]
import axios from 'axios';
import axiosRetry from 'axios-retry';

// Create a custom axios instance
const axiosInstance = axios.create({
  baseURL: 'http://localhost:3000/api',
});

// Add retry logic
axiosRetry(axiosInstance, {
  retries: 3, // Number of retry attempts
  retryDelay: (retryCount) => {
    return retryCount * 1000; // Exponential backoff
  },
  retryCondition: (error) => {
    // Retry on network errors and 5xx errors
    return axiosRetry.isNetworkOrIdempotentRequestError(error) || (error.response && error.response.status >= 500);
  },
});

// Use the custom axios instance
const db = new DatabaseSDK({
  axiosInstance,
});
```

## Performance Optimization

### Request Batching

For multiple operations, you can use batch requests to reduce the number of HTTP requests:

```typescript [ts]
// Instead of multiple individual requests
await db.table('users').create({ name: 'User 1' });
await db.table('users').create({ name: 'User 2' });
await db.table('users').create({ name: 'User 3' });

// Use batch operations
await db.table('users').createMany([{ name: 'User 1' }, { name: 'User 2' }, { name: 'User 3' }]);
```

### Selective Field Fetching

To reduce payload size, only request the fields you need:

```typescript [ts]
// Instead of fetching all fields
const users = await db.table('users').query();

// Only fetch the fields you need
const userNames = await db.table('users').select('id', 'name').query();
```

### Pagination for Large Datasets

When dealing with large datasets, use pagination to improve performance:

```typescript [ts]
// Fetch data in chunks
const pageSize = 100;
let page = 0;
let hasMore = true;
const allData = [];

while (hasMore) {
  const result = await db
    .table('large_table')
    .limit(pageSize)
    .offset(page * pageSize)
    .query();

  allData.push(...result.records);

  // Check if there's more data
  hasMore = result.records.length === pageSize;
  page++;
}
```

## Advanced Query Features

### Subqueries

You can use subqueries for more complex queries:

```typescript [ts]
// Find users who have made at least one order
const usersWithOrders = await db
  .table('users')
  .whereExists((qb) => {
    qb.table('orders').where('orders.user_id', '=', 'users.id');
  })
  .query();

// Find users with the highest order amount
const usersWithHighestOrder = await db
  .table('users')
  .select('users.*')
  .join('orders', 'users.id', '=', 'orders.user_id')
  .whereIn('orders.amount', (qb) => {
    qb.table('orders').max('amount');
  })
  .query();
```

### Window Functions

For databases that support window functions, you can use them for advanced analytics:

```typescript [ts]
// Rank users by order count
const rankedUsers = await db
  .table('users')
  .select('users.*')
  .windowFunction({
    type: 'rank',
    alias: 'rank',
    orderBy: [{ field: 'order_count', direction: 'desc' }],
  })
  .query();

// Calculate running totals
const runningTotals = await db
  .table('orders')
  .select('*')
  .windowFunction({
    type: 'sum',
    field: 'amount',
    alias: 'running_total',
    partitionBy: ['user_id'],
    orderBy: [{ field: 'created_at', direction: 'asc' }],
  })
  .query();
```

## Integration with Other Libraries

### React Query Integration

You can integrate the SDK with React Query for efficient data fetching and caching:

```typescript [ts]
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { DatabaseSDK } from '@the-forgebase/sdk/client';

// Initialize the SDK
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
});

// Custom hook for fetching users
function useUsers() {
  return useQuery('users', async () => {
    const result = await db.table('users').query();
    return result.records;
  });
}

// Custom hook for creating a user
function useCreateUser() {
  const queryClient = useQueryClient();

  return useMutation(
    async (userData) => {
      const result = await db.table('users').create(userData);
      return result.records[0];
    },
    {
      // Invalidate the users query after mutation
      onSuccess: () => {
        queryClient.invalidateQueries('users');
      },
    }
  );
}

// Usage in a component
function UsersList() {
  const { data: users, isLoading, error } = useUsers();
  const { mutate: createUser } = useCreateUser();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <button onClick={() => createUser({ name: 'New User' })}>
        Add User
      </button>
      <ul>
        {users.map((user) => (
          <li key={user.id}>{user.name}</li>
        ))}
      </ul>
    </div>
  );
}
```

### SWR Integration

You can also integrate with SWR for data fetching:

```typescript [ts]
import useSWR, { mutate } from 'swr';
import { DatabaseSDK } from '@the-forgebase/sdk/client';

// Initialize the SDK
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
});

// Fetcher function
const fetcher = async (key) => {
  const [tableName, ...params] = key.split(':');
  const result = await db.table(tableName).query();
  return result.records;
};

// Custom hook for fetching users
function useUsers() {
  return useSWR('users', fetcher);
}

// Function to create a user
async function createUser(userData) {
  const result = await db.table('users').create(userData);
  // Revalidate the users data
  mutate('users');
  return result.records[0];
}

// Usage in a component
function UsersList() {
  const { data: users, error } = useUsers();

  if (!users) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <button onClick={() => createUser({ name: 'New User' })}>
        Add User
      </button>
      <ul>
        {users.map((user) => (
          <li key={user.id}>{user.name}</li>
        ))}
      </ul>
    </div>
  );
}
```

## Conclusion

This guide covered advanced features and configurations of the ForgeBase SDK. By leveraging these advanced features, you can build more powerful and efficient applications with ForgeBase.
