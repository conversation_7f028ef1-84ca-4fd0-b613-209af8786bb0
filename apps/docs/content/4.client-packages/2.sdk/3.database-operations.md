---
title: Database Operations
description: Learn how to perform database operations with the ForgeBase SDK
icon: 'lucide:database'
---

The ForgeBase SDK provides a comprehensive set of methods for performing database operations. This guide covers the core CRUD (Create, Read, Update, Delete) operations and more advanced features.

## Reading Data

### Fetching Records

The SDK provides multiple ways to fetch records from your database:

#### Using the Query Builder

The query builder provides a fluent API for building complex queries:

```typescript [ts]
// Fetch all records from a table
const users = await db.table('users').query();

// Fetch records with conditions
const activeUsers = await db.table('users').where('status', 'active').query();

// Fetch records with multiple conditions
const activeAdmins = await db.table('users').where('status', 'active').where('role', 'admin').query();
```

#### Using Direct Methods

You can also use the direct methods on the SDK instance:

```typescript [ts]
// Fetch all records from a table
const users = await db.getRecords('users');

// Fetch records with a filter
const activeUsers = await db.getRecords('users', {
  filter: { status: 'active' },
});

// Fetch records with pagination
const paginatedUsers = await db.getRecords('users', {
  limit: 10,
  offset: 0,
});

// Fetch records with sorting
const sortedUsers = await db.getRecords('users', {
  orderBy: [{ field: 'created_at', direction: 'desc' }],
});
```

### Selecting Specific Fields

You can select specific fields to return:

```typescript [ts]
// Using the query builder
const userEmails = await db.table('users').select('id', 'email').query();

// Using direct methods
const userEmails = await db.getRecords('users', {
  select: ['id', 'email'],
});
```

### Filtering Data

The SDK provides multiple ways to filter data:

```typescript [ts]
// Simple equality filters
const activeUsers = await db.table('users').where('status', 'active').query();

// Comparison operators
const recentUsers = await db.table('users').where('created_at', '>', '2023-01-01').query();

// Multiple conditions (AND)
const activeAdmins = await db.table('users').where('status', 'active').where('role', 'admin').query();

// OR conditions
const adminsOrModerators = await db.table('users').where('role', 'admin').orWhere('role', 'moderator').query();

// IN operator
const specificUsers = await db.table('users').whereIn('id', [1, 2, 3]).query();

// NOT IN operator
const otherUsers = await db.table('users').whereNotIn('id', [1, 2, 3]).query();

// NULL checks
const usersWithoutEmail = await db.table('users').whereNull('email').query();

// NOT NULL checks
const usersWithEmail = await db.table('users').whereNotNull('email').query();
```

### Sorting Data

You can sort data using the `orderBy` method:

```typescript [ts]
// Sort by a single field (ascending by default)
const sortedUsers = await db.table('users').orderBy('name').query();

// Sort by a single field (descending)
const reverseSortedUsers = await db.table('users').orderBy('name', 'desc').query();

// Sort by multiple fields
const complexSortedUsers = await db.table('users').orderBy('role').orderBy('name', 'desc').query();
```

### Pagination

You can paginate results using the `limit` and `offset` methods:

```typescript [ts]
// Get the first 10 users
const firstPage = await db.table('users').limit(10).offset(0).query();

// Get the second page
const secondPage = await db.table('users').limit(10).offset(10).query();
```

## Creating Data

### Creating a Single Record

To create a single record, use the `create` method:

```typescript [ts]
// Using the query builder
const newUser = await db.table('users').create({
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'user',
  status: 'active',
});

// Using direct methods
const newUser = await db.createRecord('users', {
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'user',
  status: 'active',
});
```

### Creating Multiple Records

To create multiple records at once, use the `createMany` method:

```typescript [ts]
// Using the query builder
const newUsers = await db.table('users').createMany([
  {
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'user',
  },
  {
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'admin',
  },
]);

// Using direct methods
const newUsers = await db.createRecords('users', [
  {
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'user',
  },
  {
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'admin',
  },
]);
```

## Updating Data

### Updating a Single Record

To update a single record, use the `update` method:

```typescript [ts]
// Using the query builder
const updatedUser = await db.table('users').update(1, {
  name: 'John Smith',
  status: 'inactive',
});

// Using direct methods
const updatedUser = await db.updateRecord('users', 1, {
  name: 'John Smith',
  status: 'inactive',
});
```

### Updating Multiple Records

To update multiple records that match a condition, use the `updateWhere` method:

```typescript [ts]
// Using the query builder
const updatedUsers = await db.table('users').where('role', 'user').updateWhere({
  status: 'inactive',
});

// Using direct methods
const updatedUsers = await db.updateRecords(
  'users',
  { role: 'user' }, // filter
  { status: 'inactive' }, // update
);
```

## Deleting Data

### Deleting a Single Record

To delete a single record, use the `delete` method:

```typescript [ts]
// Using the query builder
await db.table('users').delete(1);

// Using direct methods
await db.deleteRecord('users', 1);
```

### Deleting Multiple Records

To delete multiple records that match a condition, use the `deleteWhere` method:

```typescript [ts]
// Using the query builder
await db.table('users').where('status', 'inactive').deleteWhere();

// Using direct methods
await db.deleteRecords('users', { status: 'inactive' });
```

## Error Handling

All database operations can throw errors. It's important to handle these errors properly:

```typescript [ts]
try {
  const users = await db.table('users').query();
  console.log('Users:', users);
} catch (error) {
  console.error('Error fetching users:', error.message);
  // Handle the error appropriately
}
```

## Next Steps

Now that you understand how to perform database operations with the ForgeBase SDK, you can explore more advanced features:

- [Query Builder](/client-packages/sdk/query-builder): Build complex queries with the fluent API
- [Authentication Integration](/client-packages/sdk/authentication): Integrate with ForgeBase Auth
- [Advanced Usage](/client-packages/sdk/advanced-usage): Explore advanced features and configurations
