---
title: Getting Started
description: Learn how to set up and use the ForgeBase SDK
icon: 'lucide:play'
---

This guide will help you get started with the ForgeBase SDK, showing you how to install it, set it up, and perform basic operations.

## Installation

First, install the ForgeBase SDK package:

:pm-install{name="@the-forgebase/sdk"}

The SDK has a peer dependency on axios, which is used for HTTP requests. If you don't already have it installed, you'll need to install it as well:

:pm-install{name="axios"}

## Basic Setup

### Initializing the SDK

The first step in using the ForgeBase SDK is to create a new instance of the `DatabaseSDK` class:

```typescript [ts]
import { DatabaseSDK } from '@the-forgebase/sdk/client';

// Initialize with your API URL
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  axiosConfig: {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  },
});
```

The `baseUrl` parameter should point to your ForgeBase API endpoint. This is typically the URL of your backend server with the API prefix.

The `axiosConfig` parameter allows you to customize the axios instance used for HTTP requests. This is useful for setting default headers, credentials, and other axios configuration options.

### Using with Authentication

If you're using ForgeBase Auth, you can integrate it with the SDK for authenticated requests:

```typescript [ts]
import { DatabaseSDK } from '@the-forgebase/sdk/client';
import { ForgebaseWebAuth } from '@the-forgebase/web-auth';

// Initialize auth
const auth = new ForgebaseWebAuth({
  apiUrl: 'http://localhost:3000/api',
});

// Option 1: Create a database SDK with the auth axios instance
const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  axiosInstance: auth.api,
});

// Option 2: Create a database SDK with auth interceptors
const authInterceptors = auth.getAuthInterceptors();
const db2 = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  authInterceptors,
});
```

## Basic Operations

### Fetching Records

To fetch records from a table, you can use the `table` method to create a query builder, then chain methods to build your query:

```typescript [ts]
// Fetch all users
const allUsers = await db.table('users').query();

// Fetch users with filtering
const activeUsers = await db.table('users').where('status', 'active').query();

// Fetch users with multiple conditions
const adminUsers = await db.table('users').where('status', 'active').where('role', 'admin').query();

// Fetch users with pagination
const paginatedUsers = await db.table('users').limit(10).offset(0).query();

// Fetch users with sorting
const sortedUsers = await db.table('users').orderBy('created_at', 'desc').query();

// Fetch specific fields
const userEmails = await db.table('users').select('id', 'email').query();
```

### Creating Records

To create a new record, use the `create` method:

```typescript [ts]
const newUser = await db.table('users').create({
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'user',
  status: 'active',
});
```

### Updating Records

To update an existing record, use the `update` method:

```typescript [ts]
const updatedUser = await db.table('users').update(1, {
  name: 'John Smith',
  status: 'inactive',
});
```

### Deleting Records

To delete a record, use the `delete` method:

```typescript [ts]
await db.table('users').delete(1);
```

## Error Handling

The SDK throws errors when operations fail. You can catch these errors to handle them gracefully:

```typescript [ts]
try {
  const users = await db.table('users').query();
  console.log('Users:', users);
} catch (error) {
  console.error('Error fetching users:', error.message);
}
```

## Next Steps

Now that you understand the basics of using the ForgeBase SDK, you can explore more advanced features:

- [Database Operations](/client-packages/sdk/database-operations): Learn more about CRUD operations
- [Query Builder](/client-packages/sdk/query-builder): Build complex queries with the fluent API
- [Authentication Integration](/client-packages/sdk/authentication): Integrate with ForgeBase Auth
- [Advanced Usage](/client-packages/sdk/advanced-usage): Explore advanced features and configurations
