---
title: Introduction
description: Overview of ForgeBase client packages for web and mobile applications
icon: 'lucide:door-open'
---

ForgeBase provides a set of client packages that allow you to interact with ForgeBase services from your web and mobile applications. These packages are designed to be easy to use, type-safe, and provide a consistent API across different platforms.

## Available Packages

### SDK

The [`@the-forgebase/sdk`](/client-packages/sdk/introduction) package provides a comprehensive client for interacting with ForgeBase services, with a focus on database operations. It includes a powerful query builder, support for real-time updates, and seamless integration with authentication.

### Web Auth

The [`@the-forgebase/web-auth`](/client-packages/web-auth/introduction) package provides authentication functionality for web applications, including sign-in, sign-up, password reset, and session management.

### React Native Auth

The [`@the-forgebase/react-native-auth`](/client-packages/react-native-auth/introduction) package provides authentication functionality for React Native applications, with native support for secure storage and biometric authentication.

## Getting Started

To get started with ForgeBase client packages, choose the package that best fits your needs and follow the installation and setup instructions in the respective documentation.

For most applications, you'll want to use the SDK package in combination with one of the authentication packages:

:pm-install{name="@the-forgebase/sdk"}

For web applications:

:pm-install{name="@the-forgebase/web-auth"}

For React Native applications:

:pm-install{name="@the-forgebase/react-native-auth"}

## Next Steps

- [SDK Documentation](/client-packages/sdk/introduction): Learn how to use the SDK package
- [Web Auth Documentation](/client-packages/web-auth/introduction): Learn how to use the Web Auth package
- [React Native Auth Documentation](/client-packages/react-native-auth/introduction): Learn how to use the React Native Auth package
