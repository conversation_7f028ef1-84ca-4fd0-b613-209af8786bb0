---
title: Database Adapters
description: Learn about the adapter system for different database engines in ForgeBase Database
icon: 'lucide:plug'
---

ForgeBase Database uses an adapter system to support multiple database engines while providing a consistent API. This guide covers the adapter system and how to work with different database engines.

## Supported Database Engines

ForgeBase Database supports all databases supported by Knex.js, with optimized adapters for the following database engines:

- **SQLite**: A lightweight, file-based database ideal for development, testing, and small applications
- **PostgreSQL**: A powerful, scalable database for production applications
- **LibSQL**: A SQLite-compatible database with additional features
- **MySQL/MariaDB**: Popular open-source databases
- **Oracle**: Enterprise-grade database
- **MSSQL**: Microsoft SQL Server

## How Adapters Work

The adapter system abstracts away database-specific features and provides a consistent interface for all database operations. When you initialize a ForgeDatabase instance, the appropriate adapter is automatically selected based on the Knex configuration.

```ts [ts]
import { ForgeDatabase } from '@the-forgebase/database';
import knex from 'knex';

// SQLite configuration
const sqliteKnex = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// PostgreSQL configuration
const pgKnex = knex({
  client: 'pg',
  connection: {
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'password',
    database: 'mydb',
  },
});

// ForgeDatabase will automatically use the appropriate adapter
const sqliteDb = new ForgeDatabase({ db: sqliteKnex });
const pgDb = new ForgeDatabase({ db: pgKnex });
```

## Adapter Features

Each adapter implements the following features:

- **SQL Dialect Handling**: Translates generic queries to database-specific SQL
- **Feature Detection**: Determines which database features are supported
- **Window Functions**: Handles window functions for advanced queries
- **Ordering**: Manages ordering, including NULL ordering
- **Identifier Sanitization**: Ensures identifiers are properly formatted

## Feature Support Matrix

Different database engines support different features. Here's a feature support matrix:

| Feature            | SQLite  | PostgreSQL | LibSQL  | MySQL/MariaDB | MSSQL       | Oracle     |
| ------------------ | ------- | ---------- | ------- | ------------- | ----------- | ---------- |
| Window Functions   | Limited | Full       | Limited | Yes (8.0+)    | Yes         | Yes        |
| CTEs               | Yes     | Yes        | Yes     | Yes (8.0+)    | Yes         | Yes        |
| Recursive CTEs     | Yes     | Yes        | Yes     | Yes (8.0+)    | Yes         | Yes        |
| NULLs Ordering     | No      | Yes        | No      | Yes (8.0+)    | Yes         | Yes        |
| JSON Operations    | Limited | Full       | Limited | Yes (5.7+)    | Yes (2016+) | Yes (12c+) |
| Array Operations   | No      | Yes        | No      | No            | No          | Yes        |
| Native UUID        | No      | Yes        | No      | No            | Yes         | No         |
| Materialized Views | No      | Yes        | No      | No            | Yes         | Yes        |
| Full-Text Search   | Yes     | Yes        | Yes     | Yes           | Yes         | Yes        |

## Using SQLite

SQLite is a lightweight, file-based database that's ideal for development, testing, and small applications.

### Configuration

```ts [ts]
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

const db = new ForgeDatabase({ db: knexInstance });
```

### SQLite-Specific Considerations

- **Performance**: SQLite is fast for read operations but can be slower for concurrent writes
- **Concurrency**: Limited support for concurrent writes
- **Data Types**: Limited data type support compared to PostgreSQL
- **Window Functions**: Limited support for window functions
- **JSON**: Basic JSON support through functions
- **File Size**: Monitor file size for large databases

## Using PostgreSQL

PostgreSQL is a powerful, scalable database that's ideal for production applications.

### Configuration

```ts [ts]
const knexInstance = knex({
  client: 'pg',
  connection: {
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'password',
    database: 'mydb',
  },
});

const db = new ForgeDatabase({ db: knexInstance });
```

### PostgreSQL-Specific Considerations

- **Performance**: Excellent performance for complex queries and large datasets
- **Concurrency**: Excellent support for concurrent operations
- **Data Types**: Rich data type support, including arrays, JSON, and custom types
- **Window Functions**: Full support for window functions
- **JSON**: Native JSON and JSONB types with powerful operators
- **Extensions**: Many extensions available for additional functionality

## Using LibSQL

LibSQL is a SQLite-compatible database with additional features.

### Configuration

```typescript [ts]
const knexInstance = knex({
  client: 'better-sqlite3',
  connection: {
    filename: './mydb.db',
  },
  useNullAsDefault: true,
});

const db = new ForgeDatabase({ db: knexInstance });
```

### LibSQL-Specific Considerations

- **Compatibility**: Compatible with SQLite
- **Performance**: Improved performance over SQLite in some scenarios
- **Features**: Additional features beyond SQLite
- **Limitations**: Similar limitations to SQLite

## Using MySQL/MariaDB

MySQL and MariaDB are popular open-source databases.

### Configuration

```ts [ts]
const knexInstance = knex({
  client: 'mysql2',
  connection: {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'password',
    database: 'mydb',
  },
});

const db = new ForgeDatabase({ db: knexInstance });
```

### MySQL-Specific Considerations

- **Performance**: Good performance for most workloads
- **Concurrency**: Good support for concurrent operations
- **Data Types**: Rich data type support
- **JSON**: JSON support in newer versions
- **Community**: Large community and extensive documentation

## Using MSSQL

Microsoft SQL Server is a powerful enterprise database.

### Configuration

```ts [ts]
const knexInstance = knex({
  client: 'mssql',
  connection: {
    server: 'localhost',
    user: 'sa',
    password: 'password',
    database: 'mydb',
  },
});

const db = new ForgeDatabase({ db: knexInstance });
```

### MSSQL-Specific Considerations

- **Performance**: Excellent performance for enterprise workloads
- **Concurrency**: Excellent support for concurrent operations
- **Data Types**: Rich data type support
- **Integration**: Strong integration with Microsoft ecosystem
- **Security**: Advanced security features

## Using Oracle

Oracle is a robust enterprise-grade database.

### Configuration

```ts [ts]
const knexInstance = knex({
  client: 'oracledb',
  connection: {
    host: 'localhost',
    user: 'system',
    password: 'password',
    database: 'XE',
  },
});

const db = new ForgeDatabase({ db: knexInstance });
```

### Oracle-Specific Considerations

- **Performance**: Excellent performance for enterprise workloads
- **Scalability**: Highly scalable for large databases
- **Data Types**: Comprehensive data type support
- **Security**: Advanced security features
- **Enterprise Features**: Many enterprise-grade features

<!-- ## Creating a Custom Adapter

If you need to support a database engine that isn't currently supported, you can create a custom adapter by implementing the `DatabaseAdapter` interface:

```ts[ts]
import { DatabaseAdapter, DatabaseFeature } from '@the-forgebase/database';
import type { WindowFunction, OrderByClause } from '@the-forgebase/database';

export class MyCustomAdapter implements DatabaseAdapter {
  buildWindowFunction(wf: WindowFunction): string {
    // Implement window function building for your database
  }

  buildOrderByClause(clauses: OrderByClause[], knex?: Knex): { column: string; order: 'asc' | 'desc'; null?: 'first' | 'last' }[] {
    // Implement order by clause building for your database
  }

  supportsFeature(feature: DatabaseFeature): boolean {
    // Indicate which features your database supports
    switch (feature) {
      case DatabaseFeature.WindowFunctions:
        return true;
      case DatabaseFeature.CTEs:
        return true;
      case DatabaseFeature.RecursiveCTEs:
        return false;
      case DatabaseFeature.NullsOrdering:
        return true;
      case DatabaseFeature.JsonOperations:
        return true;
      case DatabaseFeature.ArrayOperations:
        return false;
      default:
        return false;
    }
  }

  sanitizeIdentifier(identifier: string): string {
    // Sanitize identifiers for your database
    return identifier;
  }
}
```

Then, you can register your adapter with ForgeBase Database:

```typescript
import { getAdapter } from '@the-forgebase/database';
import { MyCustomAdapter } from './my-custom-adapter';

// Override the getAdapter function
const originalGetAdapter = getAdapter;
(getAdapter as any) = (knex: Knex): DatabaseAdapter => {
  const client = knex.client.config.client;

  if (client === 'my-custom-client') {
    return new MyCustomAdapter();
  }

  return originalGetAdapter(knex);
};
``` -->

## Best Practices

1. **Choose the Right Database**: Select the database engine that best fits your needs:

   - SQLite for development, testing, and small applications
   - PostgreSQL for production applications with complex requirements
   - MySQL/MariaDB for web applications with good community support
   - MSSQL for enterprise applications in Microsoft environments
   - Oracle for large enterprise applications with high scalability requirements
   - LibSQL for applications that need SQLite compatibility with additional features

2. **Consider Feature Requirements**: Be aware of the features supported by each database engine and choose accordingly.

3. **Test with Your Target Database**: Always test your application with the database engine you'll use in production.

4. **Handle Database-Specific Edge Cases**: Be aware of database-specific behaviors and handle them appropriately.

5. **Use Migrations**: Use a migration system to manage schema changes across different database engines.

6. **Monitor Performance**: Different database engines have different performance characteristics. Monitor and optimize accordingly.

7. **Consider Connection Pooling**: For server-based databases (PostgreSQL, MySQL, MSSQL, Oracle), configure connection pooling for optimal performance.

## Next Steps

- [Schema Management](/database/schema-management): Learn more about managing database schema
- [Data Operations](/database/data-operations): Dive deeper into data operations
- [Performance Optimization](/database/performance): Tips for optimizing database performance
