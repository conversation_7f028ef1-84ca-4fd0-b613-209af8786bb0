---
title: Schema Management
description: Learn how to manage database schema with ForgeBase Database
icon: 'lucide:table'
---

ForgeBase Database provides a powerful schema management system that allows you to create, modify, and delete tables and columns programmatically. This guide covers the schema management capabilities of the database package.

## Table Operations

### Creating Tables

To create a new table, use the `schema.create` endpoint:

```ts [ts]
await db.endpoints.schema.create({
  tableName: 'products',
  columns: [
    { name: 'id', type: 'increments', primary: true },
    { name: 'name', type: 'string', nullable: false },
    { name: 'description', type: 'text' },
    { name: 'price', type: 'decimal', precision: 10, scale: 2, nullable: false },
    { name: 'category_id', type: 'integer', references: { table: 'categories', column: 'id' } },
    { name: 'in_stock', type: 'boolean', defaultValue: true },
    { name: 'created_at', type: 'timestamp', defaultToNow: true },
    { name: 'updated_at', type: 'timestamp', defaultToNow: true },
  ],
});
```

### Deleting Tables

To delete a table, use the `schema.delete` endpoint:

```ts [ts]
await db.endpoints.schema.delete('products');
```

### Truncating Tables

To remove all data from a table without deleting the table itself, use the `schema.truncateTable` endpoint:

```ts [ts]
await db.endpoints.schema.truncateTable('products');
```

### Getting Table Information

To get information about all tables in the database:

```ts [ts]{2}
const tables = await db.endpoints.schema.getTables();
console.log(tables); // ['users', 'products', 'categories', ...]
```

To get detailed schema information for a specific table:

```ts [ts]
const tableSchema = await db.endpoints.schema.getTableSchema('products');
console.log(tableSchema);
```

## Column Operations

### Adding Columns

To add new columns to an existing table, use the `schema.modify` endpoint:

```ts [ts]
await db.endpoints.schema.modify({
  tableName: 'products',
  addColumns: [
    { name: 'discount_percentage', type: 'decimal', precision: 5, scale: 2, defaultValue: 0 },
    { name: 'is_featured', type: 'boolean', defaultValue: false },
  ],
});
```

### Modifying Columns

To modify existing columns, use the `schema.modify` endpoint:

```ts [ts]
await db.endpoints.schema.modify({
  tableName: 'products',
  modifyColumns: [
    { name: 'price', type: 'decimal', precision: 12, scale: 2 }, // Change precision
    { name: 'description', type: 'text', nullable: true }, // Make nullable
  ],
});
```

### Dropping Columns

To remove columns from a table, use the `schema.modify` endpoint:

```ts [ts]
await db.endpoints.schema.modify({
  tableName: 'products',
  dropColumns: ['discount_percentage', 'is_featured'],
});
```

## Foreign Key Operations

### Adding Foreign Keys

To add a foreign key to an existing table:

```ts [ts]
await db.endpoints.schema.addForeingKey({
  tableName: 'products',
  column: 'supplier_id',
  foreignTableName: 'suppliers',
  foreignColumn: 'id',
});
```

### Dropping Foreign Keys

To remove a foreign key:

```ts [ts]
await db.endpoints.schema.dropForeignKey({
  tableName: 'products',
  column: 'supplier_id',
});
```

## Column Types

ForgeBase Database supports a wide range of column types:

| Type         | Description                                           | Example                                                                        |
| ------------ | ----------------------------------------------------- | ------------------------------------------------------------------------------ |
| `increments` | Auto-incrementing integer, typically for primary keys | `{ name: 'id', type: 'increments', primary: true }`                            |
| `string`     | Variable-length string                                | `{ name: 'name', type: 'string', length: 255 }`                                |
| `text`       | Long text                                             | `{ name: 'description', type: 'text' }`                                        |
| `integer`    | Integer number                                        | `{ name: 'count', type: 'integer' }`                                           |
| `bigInteger` | Large integer                                         | `{ name: 'big_number', type: 'bigInteger' }`                                   |
| `boolean`    | Boolean (true/false)                                  | `{ name: 'is_active', type: 'boolean', defaultValue: true }`                   |
| `decimal`    | Decimal number with precision and scale               | `{ name: 'price', type: 'decimal', precision: 10, scale: 2 }`                  |
| `float`      | Floating-point number                                 | `{ name: 'rating', type: 'float' }`                                            |
| `datetime`   | Date and time                                         | `{ name: 'scheduled_at', type: 'datetime' }`                                   |
| `date`       | Date only                                             | `{ name: 'birth_date', type: 'date' }`                                         |
| `time`       | Time only                                             | `{ name: 'opening_time', type: 'time' }`                                       |
| `timestamp`  | Timestamp                                             | `{ name: 'created_at', type: 'timestamp', defaultToNow: true }`                |
| `binary`     | Binary data                                           | `{ name: 'data', type: 'binary' }`                                             |
| `json`       | JSON data                                             | `{ name: 'metadata', type: 'json' }`                                           |
| `jsonb`      | Binary JSON data (PostgreSQL)                         | `{ name: 'settings', type: 'jsonb' }`                                          |
| `enum`       | Enumerated type                                       | `{ name: 'status', type: 'enum', values: ['pending', 'active', 'completed'] }` |
| `uuid`       | UUID                                                  | `{ name: 'uuid', type: 'uuid' }`                                               |

## Column Constraints

You can add various constraints to columns:

### Primary Key

```ts [ts]
{ name: 'id', type: 'increments', primary: true }
```

### Unique

```ts [ts]
{ name: 'email', type: 'string', unique: true }
```

### Nullable

```ts [ts]
{ name: 'middle_name', type: 'string', nullable: true }
```

### Default Value

```ts [ts]
{ name: 'status', type: 'string', defaultValue: 'pending' }
```

### Default to Current Timestamp

```ts [ts]
{ name: 'created_at', type: 'timestamp', defaultToNow: true }
```

### Foreign Key Reference

```ts [ts]
{
  name: 'category_id',
  type: 'integer',
  references: {
    table: 'categories',
    column: 'id'
  }
}
```

## Getting Database Schema

To get the complete database schema:

```ts [ts]
const schema = await db.endpoints.schema.get();
console.log(schema);
```

The schema object contains detailed information about all tables, columns, indexes, and foreign keys in the database.

## Using Transactions

For schema operations that need to be atomic, you can use transactions:

```ts [ts]
await db.transaction(async (trx) => {
  // Create a table
  await db.endpoints.schema.create(
    {
      tableName: 'categories',
      columns: [
        { name: 'id', type: 'increments', primary: true },
        { name: 'name', type: 'string', nullable: false, unique: true },
      ],
    },
    trx,
  );

  // Create another table with a foreign key
  await db.endpoints.schema.create(
    {
      tableName: 'products',
      columns: [
        { name: 'id', type: 'increments', primary: true },
        { name: 'name', type: 'string', nullable: false },
        { name: 'category_id', type: 'integer', references: { table: 'categories', column: 'id' } },
      ],
    },
    trx,
  );
});
```

## Best Practices

1. **Use Migrations**: For production applications, consider using a migration system to manage schema changes over time.

2. **Plan Your Schema**: Design your database schema carefully before implementation to avoid frequent changes.

3. **Use Transactions**: Wrap related schema operations in transactions to ensure atomicity.

4. **Set Permissions**: After creating tables, set appropriate permissions to control access.

5. **Consider Indexes**: Add indexes to columns that are frequently used in WHERE clauses or joins to improve performance.

6. **Validate Data**: Use constraints like NOT NULL and UNIQUE to enforce data integrity at the database level.

7. **Document Your Schema**: Keep documentation of your database schema for reference.

## Next Steps

- [Data Operations](/database/data-operations): Learn how to perform CRUD operations
- [Row-Level Security](/database/row-level-security): Implement fine-grained access control
- [Permissions](/database/permissions): Manage role-based permissions
