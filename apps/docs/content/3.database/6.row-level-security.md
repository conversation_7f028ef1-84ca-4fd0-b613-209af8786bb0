---
title: Row-Level Security
description: Learn how to implement fine-grained access control with ForgeBase Database's row-level security
icon: 'lucide:shield'
---

Row-Level Security (RLS) is a powerful feature of ForgeBase Database that allows you to control which rows in a table a user can access. This guide covers how to implement and use RLS in your applications.

## Understanding RLS

Row-Level Security applies filters to database operations to restrict which rows can be accessed by a user. These filters are applied automatically when a user performs a query, update, or delete operation.

With RLS, you can:

- Restrict users to only see their own data
- Allow managers to see data for their department
- Implement multi-tenancy where each tenant can only access their own data
- Create complex access patterns based on user attributes and relationships

## Enabling RLS

RLS is enabled by default when you create a ForgeDatabase instance with the `enforceRls` option set to `true`:

```ts [ts]
const db = new ForgeDatabase({
  db: knexInstance,
  enforceRls: true, // Enable row-level security
});
```

## RLS Policies

RLS policies are defined as part of the table permissions. Each operation (SELECT, INSERT, UPDATE, DELETE) can have its own RLS policy.

### Basic RLS Policy Structure

```ts [ts]
const permissions = {
  operations: {
    SELECT: [
      {
        allow: 'auth', // Only authenticated users
        fieldCheck: {
          field: 'user_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      {
        allow: 'role', // Specific roles
        roles: ['admin'],
      },
    ],
    // Similar structures for INSERT, UPDATE, DELETE
  },
};

await db.setPermissions('posts', permissions);
```

## RLS Policy Examples

### User Can Only See Their Own Data

```ts [ts]
const permissions = {
  operations: {
    SELECT: [
      {
        allow: 'role',
        roles: ['admin'], // Admins can see all data
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'user_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
  },
};
```

### Department Manager Can See Department Data

```ts [ts]
const permissions = {
  operations: {
    SELECT: [
      {
        allow: 'role',
        roles: ['admin'], // Admins can see all data
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'department_id',
          operator: '===',
          valueType: 'userContext',
          value: 'departmentId',
        },
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'user_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
  },
};
```

### Multi-Tenant Application

```ts [ts]
const permissions = {
  operations: {
    SELECT: [
      {
        allow: 'role',
        roles: ['admin'], // Admins can see all data
      },
      {
        allow: 'customSql',
        customSql: `
          SELECT 1 WHERE
            tenant_id = :tenantId
            AND (
              user_id = :userId
              OR is_public = true
              OR EXISTS (
                SELECT 1 FROM tenant_admins
                WHERE tenant_id = :tenantId AND user_id = :userId
              )
            )
        `,
      },
    ],
  },
};
```

### Public and Private Data

```ts [ts]
const permissions = {
  operations: {
    SELECT: [
      {
        allow: 'role',
        roles: ['admin'], // Admins can see all data
      },
      {
        allow: 'customSql',
        customSql: `
          SELECT 1 WHERE
            is_public = true
            OR (user_id = :userId AND :userId IS NOT NULL)
        `,
      },
    ],
  },
};
```

## User Context

The user context is an object that contains information about the current user. This information is used to evaluate RLS policies. The user context is passed as a parameter to data operations:

```ts [ts]
const posts = await db.endpoints.data.query(
  'posts',
  {
    select: ['id', 'title', 'content'],
  },
  {
    userId: 123, // User ID
    role: 'user', // User role
    departmentId: 5, // Additional user attributes
    tenantId: 42,
    teams: ['team1', 'team2'], // For team-based access
    labels: ['admin', 'editor'], // For label-based access
    // Any other attributes needed for RLS policies
  },
);
```

## Complex RLS Rules

ForgeBase Database supports several ways to create complex RLS rules:

### Custom SQL Rules

You can use the `customSql` rule type for complex database queries:

```ts [ts]
{
  allow: 'customSql',
  customSql: `
    SELECT 1 WHERE
      user_id = :userId
      OR (is_public = true AND category_id IN (1, 2, 3))
      OR user_id IN (SELECT user_id FROM team_members WHERE team_id IN (:teams))
      OR (published_at <= CURRENT_TIMESTAMP AND is_public = true)
  `,
}
```

### Custom Function Rules

For the most flexible rules, you can register custom JavaScript functions:

```ts [ts]
// Register a custom function
rlsFunctionRegistry.register('canAccessDocument', async (userContext, row, knex) => {
  // Check if user is the owner
  if (row.user_id === userContext.userId) return true;

  // Check if document is public
  if (row.is_public) return true;

  // Check if user is in the document's team
  if (knex && row.team_id && userContext.teams) {
    const isMember = userContext.teams.includes(row.team_id);
    if (isMember) return true;
  }

  return false;
});

// Use the function in permissions
{
  allow: 'customFunction',
  customFunction: 'canAccessDocument',
}
```

## Bypassing RLS

In some cases, you may need to bypass RLS for administrative operations. You can do this by setting the `isSystem` parameter to `true`:

```ts [ts]
// Bypass RLS for this operation
const allPosts = await db.endpoints.data.query(
  'posts',
  {
    select: ['id', 'title', 'user_id'],
  },
  { userId: 1, role: 'admin' }, // User context
  true, // isSystem flag to bypass RLS
);
```

## Testing RLS Policies

It's important to test your RLS policies to ensure they work as expected:

```ts[ts]
// Test as admin (should see all posts)
const adminPosts = await db.endpoints.data.query('posts', { select: ['id', 'title'] }, { userId: 1, role: 'admin' });
console.log('Admin can see', adminPosts.length, 'posts');

// Test as user (should only see their own posts)
const userPosts = await db.endpoints.data.query('posts', { select: ['id', 'title'] }, { userId: 2, role: 'user' });
console.log('User can see', userPosts.length, 'posts');

// Test as anonymous (should only see public posts)
const anonymousPosts = await db.endpoints.data.query('posts', { select: ['id', 'title'] }, { role: 'anonymous' });
console.log('Anonymous can see', anonymousPosts.length, 'posts');
```

## RLS for Different Operations

You can define different RLS policies for different operations:

```ts [ts]
const permissions = {
  operations: {
    SELECT: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'customSql',
        customSql: `
          SELECT 1 WHERE
            is_public = true
            OR (user_id = :userId AND :userId IS NOT NULL)
        `,
      },
    ],
    INSERT: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'auth', // Authenticated users can create
      },
    ],
    UPDATE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'user_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    DELETE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'customSql',
        customSql: `
          SELECT 1 WHERE
            user_id = :userId
            AND created_at > (CURRENT_TIMESTAMP - INTERVAL '1 day')
        `,
      },
    ],
  },
};
```

## Performance Considerations

RLS adds additional filtering to queries, which can impact performance. Here are some tips to optimize RLS performance:

1. **Index Columns Used in RLS Rules**: Ensure that columns used in RLS rules are properly indexed.

2. **Keep Rules Simple**: Complex rules can slow down queries. Keep rules as simple as possible.

3. **Avoid Subqueries When Possible**: Subqueries can be expensive. Consider denormalizing data if necessary.

4. **Use Caching**: Consider caching results for frequently accessed data.

5. **Monitor Query Performance**: Regularly monitor query performance and optimize as needed.

## Best Practices

1. **Default to Deny**: Start with restrictive policies and add permissions as needed.

2. **Test Thoroughly**: Test RLS policies with different user contexts to ensure they work as expected.

3. **Document Policies**: Document your RLS policies for future reference.

4. **Use Role-Based Policies**: Organize policies by role for clarity and maintainability.

5. **Consider Edge Cases**: Think about edge cases like null values, empty arrays, etc.

6. **Audit Access**: Implement logging to audit data access.

7. **Regular Reviews**: Regularly review and update RLS policies as your application evolves.

## Next Steps

- [Permissions](/database/permissions): Learn more about the permission system
- [Data Operations](/database/data-operations): Dive deeper into data operations
- [Transactions](/database/transactions): Use transactions for data integrity
