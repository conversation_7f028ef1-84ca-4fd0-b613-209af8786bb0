---
title: Features
description: Comprehensive overview of ForgeBase Database features and capabilities
icon: 'lucide:list-checks'
---

ForgeBase Database provides a comprehensive set of features for building powerful, secure, and scalable database-driven applications. This page provides a detailed overview of all the capabilities available in the package.

## Dynamic Schema Management

ForgeBase Database allows you to create and manage your database schema programmatically:

- **Multi-database Support**: Works with SQLite, PostgreSQL, MySQL, MSSQL, and any Knex-compatible database
- **Dynamic Table Creation**: Create and delete tables dynamically via API
- **Rich Column Types**: Support for various column types:
  - `string`: Text data with optional length constraints
  - `text`: Long-form text data
  - `integer`: Whole number values
  - `bigInteger`: Large whole number values
  - `decimal`: Precise decimal numbers with configurable precision and scale
  - `float`: Floating-point numbers
  - `boolean`: True/false values
  - `date`: Date values
  - `datetime`: Date and time values
  - `timestamp`: Timestamp values with timezone support
  - `json`: JSON data structures
  - `jsonb`: Binary JSON data (PostgreSQL)
  - `uuid`: Universally unique identifiers
  - `binary`: Binary data
  - `enum`: Enumerated types with predefined values
- **Constraint Support**:
  - Automatic primary key handling
  - Unique constraints
  - Not null constraints
  - Default values
  - Foreign key relationships
- **Schema Modification**: Add, modify, or remove columns from existing tables
- **Index Management**: Create and manage indexes for optimized queries

## Data Operations

ForgeBase Database provides a comprehensive set of data operations:

- **CRUD Operations**: Create, read, update, and delete operations for any table
- **Query Building**:
  - Complex filtering with multiple conditions
  - Sorting by multiple columns
  - Pagination with limit and offset
  - Column selection
  - Table joins
  - Aggregation functions
- **Advanced Queries**:
  - Subqueries
  - Window functions
  - Common Table Expressions (CTEs)
  - Group by and having clauses
- **Safe Parameter Handling**: Automatic parameter binding to prevent SQL injection
- **Transactions**: Support for atomic operations across multiple tables
- **Bulk Operations**: Efficient bulk insert, update, and delete operations

## Database Inspection

Inspect and understand your database structure:

- **Schema Retrieval**: Get the complete database schema
- **Table Inspection**: View detailed information about table structure
- **Column Information**: Get metadata about columns including types, constraints, and defaults
- **Foreign Key Relationships**: Discover relationships between tables
- **Index Information**: View indexes and their properties

## Event Hooks System

ForgeBase Database includes a powerful event system for extending functionality:

- **Query Hooks**:
  - Before query execution
  - After query execution
- **Mutation Hooks**:
  - Before create/update/delete operations
  - After create/update/delete operations
- **Real-time Operation Logging**: Track all database operations
- **Extensible Event System**: Register custom event handlers for specific operations
- **Error Handling**: Custom error handling for database operations

## Row-Level Security (RLS)

Fine-grained access control at the row level:

- **Operation-specific Rules**: Different rules for SELECT, INSERT, UPDATE, and DELETE operations
- **Multiple Permission Rule Types**:
  - `public`: Access for all users (authenticated and unauthenticated)
  - `auth`: Access only for authenticated users
  - `role`: Access based on user roles
  - `labels`: Access based on user labels
  - `teams`: Access based on team membership
  - `fieldCheck`: Access based on field value comparison with user context
  - `customSql`: Access based on custom SQL conditions
  - `customFunction`: Access based on custom JavaScript functions
- **Field-Value Checks**: Compare field values with user context attributes
- **Real-time Permission Evaluation**: Permissions are evaluated for each request
- **Permission Persistence**: Permissions are stored in the database for persistence
- **System Operations**: Ability to bypass RLS for administrative operations

## Permission Management

Comprehensive permission management system:

- **CRUD for Permissions**: Create, read, update, and delete permissions for tables
- **Operation-specific Rules**: Configure different permissions for SELECT, INSERT, UPDATE, and DELETE operations
- **Flexible Rule Configuration**: Combine multiple rule types for complex permission scenarios
- **Database-backed Storage**: Permissions are stored in the database for persistence
- **Default Permissions**: Configure default permissions for new tables
- **Permission Initialization**: Automatically initialize permissions for all tables
- **Permission Caching**: Efficient permission caching for improved performance

## Real-time Updates

::alert{type="warning" icon="lucide:triangle-alert"}
This is not yet completely finished
::

Keep your application data in sync with real-time updates:

- **WebSocket Server**: Built-in WebSocket server for real-time updates
- **Change Broadcasting**: Broadcast database changes to connected clients
- **Table Subscriptions**: Subscribe to specific tables for updates
- **Filtered Subscriptions**: Subscribe to specific queries with filters
- **Event Types**: Different event types for create, update, and delete operations
- **Client Reconnection**: Automatic client reconnection handling
- **Authentication**: Support for authenticated WebSocket connections

## Type Safety

Full TypeScript support for type-safe database operations:

- **Type Definitions**: Comprehensive type definitions for all database operations
- **Generic Types**: Generic type parameters for table data
- **IDE Integration**: Intellisense support in modern IDEs
- **Compile-time Checking**: Catch errors at compile time rather than runtime
- **Type Inference**: Automatic type inference for query results

## Adapter System

::alert{type="danger" icon="lucide:circle-x"}
This is not yet finalized, do not use
::
Extensible adapter system for different database engines:

- **Multi-database Support**: Support for SQLite, PostgreSQL, MySQL, MSSQL, and more
- **Feature Detection**: Automatic detection of database features
- **SQL Dialect Handling**: Automatic translation of queries to database-specific SQL
- **Custom Adapters**: Ability to create custom adapters for unsupported databases

## Integration Options

Multiple ways to integrate ForgeBase Database into your application:

- **API Package**: Use the `@the-forgebase/api` package for a ready-to-use REST API
- **Frontend SDK**: Use the `@the-forgebase/sdk` package for type-safe client-side operations
- **Direct REST API**: Access your database directly through REST API endpoints
- **Custom Framework Integration**: Use the database package directly in frameworks like Hono, Express, or Next.js
- **Authentication Integration**: Seamless integration with ForgeBase Auth

## Next Steps

Now that you understand the features of ForgeBase Database, you can dive deeper into specific areas:

- [Getting Started](/database/getting-started): Learn how to set up and use the database package
- [Schema Management](/database/schema-management): Manage database schema
- [Data Operations](/database/data-operations): Perform CRUD operations
- [Row-Level Security](/database/row-level-security): Implement fine-grained access control
- [Permissions](/database/permissions): Manage role-based permissions
- [Complete Integration](/database/complete-integration): Use ForgeBase Database with frontend SDK, REST API, and custom frameworks
