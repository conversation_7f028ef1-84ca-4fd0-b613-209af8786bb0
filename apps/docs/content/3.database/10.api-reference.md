---
title: API Reference
description: Detailed API documentation for ForgeBase Database
icon: 'lucide:book'
---

This page provides detailed API documentation for the ForgeBase Database package.

## ForgeDatabase Class

The main class for interacting with the database.

### Constructor

```typescript
constructor(config: ForgeDatabaseConfig)
```

#### Parameters

- `config`: Configuration options for the database
  - `db`: Knex instance
  - `hooks`: Optional KnexHooks instance
  - `permissionsService`: Optional PermissionService instance
  - `prefix`: Optional prefix for table names
  - `enforceRls`: Whether to enforce row-level security (default: `true`)
  - `realtime`: Whether to enable real-time updates (default: `false`)
  - `realtimeAdapter`: Type of realtime adapter to use (default: `'websocket'`)
  - `defaultPermissions`: Default permissions for new tables
  - `excludedTables`: Tables to exclude from operations
  - `websocketPort`: Port for the WebSocket server (default: `8080`)
  - `initializePermissions`: Whether to automatically initialize permissions for all tables (default: `false`)
  - `permissionReportPath`: Path where the permission initialization report will be saved
  - `onPermissionInitComplete`: Callback function to be called when permission initialization is complete

### Methods

#### getKnexInstance

```typescript
getKnexInstance(): Knex
```

Returns the Knex instance used by the database.

#### getPermissionService

```typescript
getPermissionService(): PermissionService
```

Returns the permission service used by the database.

#### getHooksDb

```typescript
getHooksDb(): KnexHooks
```

Returns the KnexHooks instance used by the database.

#### transaction

```typescript
async transaction<T>(callback: (trx: Knex.Transaction) => Promise<T>): Promise<T>
```

Executes a function within a transaction.

##### Parameters

- `callback`: Function to execute within the transaction

##### Returns

Result of the callback function

#### initializeTablePermissions

```typescript
async initializeTablePermissions(): Promise<PermissionInitializationReport>
```

Initializes permissions for all tables in the database.

##### Returns

A report of the initialization process

### Endpoints

The ForgeDatabase class provides several endpoints for interacting with the database:

#### schema

Endpoints for managing database schema:

##### get

```typescript
async get(): Promise<DatabaseSchema>
```

Gets the database schema.

##### create

```typescript
async create(
  params: SchemaCreateParams,
  trx?: Knex.Transaction
): Promise<{
  message: string;
  tablename: string;
  action: string;
}>
```

Creates a new table.

###### Parameters

- `params`: Parameters for creating the table
  - `tableName`: Name of the table to create
  - `columns`: Column definitions
- `trx`: Optional transaction

##### delete

```typescript
async delete(
  tableName: string,
  trx?: Knex.Transaction
): Promise<{
  message: string;
  tablename: string;
  action: string;
}>
```

Deletes a table.

###### Parameters

- `tableName`: Name of the table to delete
- `trx`: Optional transaction

##### modify

```typescript
async modify(
  params: ModifySchemaParams,
  trx?: Knex.Transaction
): Promise<any>
```

Modifies a table's schema.

###### Parameters

- `params`: Parameters for modifying the table
  - `tableName`: Name of the table to modify
  - `addColumns`: Columns to add
  - `dropColumns`: Columns to drop
  - `modifyColumns`: Columns to modify
- `trx`: Optional transaction

##### addForeingKey

```typescript
async addForeingKey(
  params: AddForeignKeyParams,
  trx?: Knex.Transaction
): Promise<any>
```

Adds a foreign key to a table.

###### Parameters

- `params`: Parameters for adding the foreign key
  - `tableName`: Name of the table
  - `column`: Column name
  - `foreignTableName`: Foreign table name
  - `foreignColumn`: Foreign column name
- `trx`: Optional transaction

##### dropForeignKey

```typescript
async dropForeignKey(
  params: DropForeignKeyParams,
  trx?: Knex.Transaction
): Promise<any>
```

Drops a foreign key from a table.

###### Parameters

- `params`: Parameters for dropping the foreign key
  - `tableName`: Name of the table
  - `column`: Column name
- `trx`: Optional transaction

##### truncateTable

```typescript
async truncateTable(
  tableName: string,
  trx?: Knex.Transaction
): Promise<any>
```

Truncates a table (removes all data).

###### Parameters

- `tableName`: Name of the table to truncate
- `trx`: Optional transaction

##### getTables

```typescript
async getTables(): Promise<string[]>
```

Gets all table names in the database.

##### getTableSchema

```typescript
async getTableSchema(tableName: string): Promise<{
  table: TableInfo;
  columns: ColumnInfo[];
}>
```

Gets the schema for a specific table.

###### Parameters

- `tableName`: Name of the table

#### data

Endpoints for managing data:

##### query

```typescript
async query<T>(
  tableName: string,
  params: DataQueryParams,
  user?: UserContext,
  isSystem?: boolean,
  trx?: Knex.Transaction
): Promise<T[]>
```

Queries data from a table.

###### Parameters

- `tableName`: Name of the table to query
- `params`: Query parameters
  - `select`: Columns to select
  - `where`: Simple equality conditions
  - `filter`: Complex filter conditions
  - `orderBy`: Sorting criteria
  - `limit`: Maximum number of records
  - `offset`: Number of records to skip
  - `join`: Table joins
  - `groupBy`: Grouping columns
  - `having`: Filters for grouped results
  - `whereExists`: Existence subquery
  - `whereNotExists`: Non-existence subquery
  - `distinct`: Return distinct results
- `user`: User context for row-level security
- `isSystem`: Whether this is a system operation (bypasses RLS)
- `trx`: Optional transaction

##### create

```typescript
async create(
  params: DataMutationParams,
  user?: UserContext,
  isSystem?: boolean,
  trx?: Knex.Transaction
): Promise<any>
```

Creates one or more records in a table.

###### Parameters

- `params`: Parameters for creating the record(s)
  - `tableName`: Name of the table
  - `data`: Data to insert (object or array of objects)
- `user`: User context for row-level security
- `isSystem`: Whether this is a system operation (bypasses RLS)
- `trx`: Optional transaction

##### update

```typescript
async update(
  params: DataMutationParams,
  user?: UserContext,
  isSystem?: boolean,
  trx?: Knex.Transaction
): Promise<any>
```

Updates a record in a table by ID.

###### Parameters

- `params`: Parameters for updating the record
  - `tableName`: Name of the table
  - `id`: ID of the record to update
  - `data`: Data to update
- `user`: User context for row-level security
- `isSystem`: Whether this is a system operation (bypasses RLS)
- `trx`: Optional transaction

##### advanceUpdate

```typescript
async advanceUpdate(
  params: AdvanceDataMutationParams,
  user?: UserContext,
  isSystem?: boolean,
  trx?: Knex.Transaction
): Promise<any>
```

Updates records in a table based on a filter.

###### Parameters

- `params`: Parameters for updating the records
  - `tableName`: Name of the table
  - `filter`: Filter to select records to update
  - `data`: Data to update
- `user`: User context for row-level security
- `isSystem`: Whether this is a system operation (bypasses RLS)
- `trx`: Optional transaction

##### delete

```typescript
async delete(
  params: DataDeleteParams,
  user?: UserContext,
  isSystem?: boolean,
  trx?: Knex.Transaction
): Promise<any>
```

Deletes a record from a table by ID.

###### Parameters

- `params`: Parameters for deleting the record
  - `tableName`: Name of the table
  - `id`: ID of the record to delete
- `user`: User context for row-level security
- `isSystem`: Whether this is a system operation (bypasses RLS)
- `trx`: Optional transaction

##### advanceDelete

```typescript
async advanceDelete(
  params: AdvanceDataDeleteParams,
  user?: UserContext,
  isSystem?: boolean,
  trx?: Knex.Transaction
): Promise<any>
```

Deletes records from a table based on a filter.

###### Parameters

- `params`: Parameters for deleting the records
  - `tableName`: Name of the table
  - `filter`: Filter to select records to delete
- `user`: User context for row-level security
- `isSystem`: Whether this is a system operation (bypasses RLS)
- `trx`: Optional transaction

## PermissionService Class

Service for managing table permissions.

### Constructor

```typescript
constructor(knex: Knex)
```

#### Parameters

- `knex`: Knex instance

### Methods

#### getPermissionsForTable

```typescript
async getPermissionsForTable(
  tableName: string,
  trx?: Knex.Transaction
): Promise<TablePermissions>
```

Gets permissions for a table.

##### Parameters

- `tableName`: Name of the table
- `trx`: Optional transaction

##### Returns

Table permissions

#### setPermissionsForTable

```typescript
async setPermissionsForTable(
  tableName: string,
  permissions: TablePermissions,
  trx?: Knex.Transaction
): Promise<TablePermissions>
```

Sets permissions for a table.

##### Parameters

- `tableName`: Name of the table
- `permissions`: Permissions to set
- `trx`: Optional transaction

##### Returns

Updated table permissions

#### deletePermissionsForTable

```typescript
async deletePermissionsForTable(
  tableName: string,
  trx?: Knex.Transaction
): Promise<void>
```

Deletes permissions for a table.

##### Parameters

- `tableName`: Name of the table
- `trx`: Optional transaction

## KnexHooks Class

Class for hooking into Knex queries to add functionality like real-time updates.

### Constructor

```typescript
constructor(knex: Knex, realtimeAdapter?: RealtimeAdapter)
```

#### Parameters

- `knex`: Knex instance
- `realtimeAdapter`: Optional realtime adapter

### Methods

#### getKnexInstance

```typescript
getKnexInstance(): Knex
```

Returns the Knex instance.

#### query

```typescript
async query<T>(
  tableName: string,
  queryBuilder: (query: Knex.QueryBuilder) => Knex.QueryBuilder,
  params: any,
  trx?: Knex.Transaction
): Promise<T[]>
```

Executes a query with hooks.

##### Parameters

- `tableName`: Name of the table
- `queryBuilder`: Function to build the query
- `params`: Query parameters
- `trx`: Optional transaction

##### Returns

Query results

#### insert

```typescript
async insert(
  tableName: string,
  data: any | any[],
  trx?: Knex.Transaction
): Promise<any>
```

Inserts data with hooks.

##### Parameters

- `tableName`: Name of the table
- `data`: Data to insert
- `trx`: Optional transaction

##### Returns

Inserted data

#### update

```typescript
async update(
  tableName: string,
  id: number | string,
  data: any,
  trx?: Knex.Transaction
): Promise<any>
```

Updates data with hooks.

##### Parameters

- `tableName`: Name of the table
- `id`: ID of the record to update
- `data`: Data to update
- `trx`: Optional transaction

##### Returns

Updated data

#### advanceUpdate

```typescript
async advanceUpdate(
  tableName: string,
  filter: any,
  data: any,
  trx?: Knex.Transaction
): Promise<any>
```

Updates data with hooks based on a filter.

##### Parameters

- `tableName`: Name of the table
- `filter`: Filter to select records to update
- `data`: Data to update
- `trx`: Optional transaction

##### Returns

Update result

#### delete

```typescript
async delete(
  tableName: string,
  id: number | string,
  trx?: Knex.Transaction
): Promise<any>
```

Deletes data with hooks.

##### Parameters

- `tableName`: Name of the table
- `id`: ID of the record to delete
- `trx`: Optional transaction

##### Returns

Delete result

#### advanceDelete

```typescript
async advanceDelete(
  tableName: string,
  filter: any,
  trx?: Knex.Transaction
): Promise<any>
```

Deletes data with hooks based on a filter.

##### Parameters

- `tableName`: Name of the table
- `filter`: Filter to select records to delete
- `trx`: Optional transaction

##### Returns

Delete result

## Types

### ForgeDatabaseConfig

Configuration options for the ForgeDatabase class.

```typescript
interface ForgeDatabaseConfig {
  db?: Knex;
  hooks?: KnexHooks;
  permissionsService?: PermissionService;
  prefix?: string;
  enforceRls?: boolean;
  realtime?: boolean;
  realtimeAdapter?: RealtimeAdapterType;
  defaultPermissions?: TablePermissions;
  excludedTables?: string[];
  websocketPort?: number;
  initializePermissions?: boolean;
  permissionReportPath?: string;
  onPermissionInitComplete?: (report: PermissionInitializationReport) => void;
}
```

### TablePermissions

Permissions for a table.

```typescript
type TablePermissions = {
  operations: {
    SELECT?: PermissionRule[];
    INSERT?: PermissionRule[];
    UPDATE?: PermissionRule[];
    DELETE?: PermissionRule[];
  };
};

type CustomRlsFunction = (userContext: UserContext, row: Record<string, unknown>, knex?: Knex) => Promise<boolean> | boolean;

type PermissionRule = {
  allow: 'public' | 'private' | 'role' | 'auth' | 'guest' | 'labels' | 'teams' | 'static' | 'fieldCheck' | 'customSql' | 'customFunction';
  labels?: string[]; // Array of required labels
  teams?: string[]; // Array of required teams
  static?: boolean; // Static true/false value
  customSql?: string; // Custom SQL condition (full SQL)
  customFunction?: string; // Name of registered custom function
  fieldCheck?: FieldCheck; // Field-based rules
  roles?: string[];
};

type FieldCheck = {
  field: string; // Field to check on the row/data being fetched or mutated
  operator: '===' | '!==' | 'in' | 'notIn'; // Comparison operators
  valueType: 'userContext' | 'static'; // Whether to compare against userContext or a static value
  value: UserContextFields | any[]; // Updated to use UserContextFields when valueType is "userContext"
};
```

### UserContext

User context for row-level security.

```typescript
type UserContext = {
  userId: number | string;
  labels: string[];
  teams: string[];
  permissions?: string[]; // Optional explicit permissions
  role?: string;
};
```

### ColumnDefinition

Definition for a table column.

```typescript
interface ColumnDefinition {
  name: string;
  type: ColumnType;
  primary?: boolean;
  unique?: boolean;
  nullable: boolean;
  enumValues?: string[];
  foreignKeys?: ForeignKey;
  default?: any;
}

interface UpdateColumnDefinition {
  currentName: string;
  newName?: string;
  type?: ColumnType;
  currentType: ColumnType; // Added this to know the current column type
  primary?: boolean;
  unique?: boolean;
  nullable?: boolean;
  foreignKeys?: ForeignKey;
  default?: any;
}

interface ForeignKey {
  columnName: string;
  references: {
    tableName: string;
    columnName: string;
  };
}
```

### ColumnType

Supported column types.

```typescript
type ColumnType = 'increments' | 'string' | 'text' | 'integer' | 'bigInteger' | 'boolean' | 'decimal' | 'float' | 'datetime' | 'date' | 'time' | 'timestamp' | 'binary' | 'json' | 'jsonb' | 'enum' | 'uuid';
```

### SchemaCreateParams

Parameters for creating a table.

```typescript
interface SchemaCreateParams {
  tableName: string;
  columns: ColumnDefinition[];
}
```

### ModifySchemaParams

Parameters for modifying a table.

```typescript
interface ModifySchemaParams {
  tableName: string;
  action: 'addColumn' | 'deleteColumn' | 'updateColumn';
  columns: ColumnDefinition[] | UpdateColumnDefinition[];
}
```

### DataQueryParams

Parameters for querying data.

```typescript
interface DataQueryParams {
  select?: (string | { fn: string; args: any[]; as: string })[];
  where?: { [key: string]: any };
  filter?: { [key: string]: any };
  orderBy?: { column: string; direction: 'asc' | 'desc' }[];
  limit?: number;
  offset?: number;
  join?: {
    table: string;
    on: { [key: string]: string };
    type?: 'inner' | 'left' | 'right' | 'full';
  }[];
  groupBy?: string[];
  having?: { [key: string]: any };
  whereExists?: {
    table: string;
    on: { [key: string]: string };
    where?: { [key: string]: any };
  };
  whereNotExists?: {
    table: string;
    on: { [key: string]: string };
    where?: { [key: string]: any };
  };
  distinct?: boolean;
}
```

### DataMutationParams

Parameters for creating or updating data.

```typescript
interface DataMutationParams {
  tableName: string;
  id?: number | string;
  data: any | any[];
}
```

### AdvanceDataMutationParams

Parameters for advanced update operations.

```typescript
interface AdvanceDataMutationParams {
  tableName: string;
  filter: { [key: string]: any };
  data: any;
}
```

### DataDeleteParams

Parameters for deleting data.

```typescript
interface DataDeleteParams {
  tableName: string;
  id: number | string;
}
```

### AdvanceDataDeleteParams

Parameters for advanced delete operations.

```typescript
interface AdvanceDataDeleteParams {
  tableName: string;
  filter: { [key: string]: any };
}
```

## Error Classes

### AuthenticationRequiredError

Thrown when authentication is required but not provided.

```typescript
class AuthenticationRequiredError extends Error {
  constructor(message?: string);
}
```

### PermissionDeniedError

Thrown when a user does not have permission to perform an operation.

```typescript
class PermissionDeniedError extends Error {
  constructor(message?: string);
}
```

### ExcludedTableError

Thrown when an operation is attempted on an excluded table.

```typescript
class ExcludedTableError extends Error {
  constructor(tableName: string);
}
```

## Next Steps

- [Getting Started](/database/getting-started): Learn how to set up and use the database package
- [Schema Management](/database/schema-management): Manage database schema
- [Data Operations](/database/data-operations): Perform CRUD operations
- [Row-Level Security](/database/row-level-security): Implement fine-grained access control
