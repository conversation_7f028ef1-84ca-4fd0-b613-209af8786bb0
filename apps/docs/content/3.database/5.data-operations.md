---
title: Data Operations
description: Learn how to perform CRUD operations with ForgeBase Database
icon: 'lucide:database'
---

ForgeBase Database provides a comprehensive set of data operations for creating, reading, updating, and deleting data. This guide covers the data operation capabilities of the database package.

## Query Operations

### Basic Queries

To query data from a table, use the `data.query` endpoint:

```ts [ts]
const users = await db.endpoints.data.query(
  'users',
  {
    select: ['id', 'username', 'email', 'role'],
  },
  { id: 1, role: 'admin' }, // User context for RLS
);
```

### Filtering Data

You can filter data using the `where` parameter:

```ts [ts]
const activeUsers = await db.endpoints.data.query(
  'users',
  {
    select: ['id', 'username', 'email'],
    where: { status: 'active' },
  },
  { id: 1, role: 'admin' },
);
```

For more complex filters, you can use the `filter` parameter with operators:

```ts [ts]
const filteredProducts = await db.endpoints.data.query(
  'products',
  {
    select: ['id', 'name', 'price', 'category_id'],
    filter: {
      price: { $gt: 100, $lt: 500 },
      category_id: { $in: [1, 2, 3] },
      name: { $like: '%phone%' },
    },
  },
  { id: 1, role: 'admin' },
);
```

### Sorting Data

To sort the results, use the `orderBy` parameter:

```ts [ts]
const sortedUsers = await db.endpoints.data.query(
  'users',
  {
    select: ['id', 'username', 'created_at'],
    orderBy: [
      { column: 'created_at', direction: 'desc' },
      { column: 'username', direction: 'asc' },
    ],
  },
  { id: 1, role: 'admin' },
);
```

### Pagination

For pagination, use the `limit` and `offset` parameters:

```ts [ts]
const paginatedUsers = await db.endpoints.data.query(
  'users',
  {
    select: ['id', 'username', 'email'],
    limit: 10,
    offset: 20, // Skip the first 20 records
  },
  { id: 1, role: 'admin' },
);
```

### Joining Tables

To join related tables, use the `join` parameter:

```ts [ts]
const usersWithPosts = await db.endpoints.data.query(
  'users',
  {
    select: ['users.id', 'users.username', 'posts.id as post_id', 'posts.title'],
    join: [
      {
        table: 'posts',
        on: { 'users.id': 'posts.user_id' },
        type: 'left', // 'inner', 'left', 'right', 'full'
      },
    ],
  },
  { id: 1, role: 'admin' },
);
```

### Aggregation

For aggregation queries, use the `groupBy` and `having` parameters:

```ts [ts]
const postCountByUser = await db.endpoints.data.query(
  'posts',
  {
    select: ['user_id', { fn: 'count', args: ['id'], as: 'post_count' }],
    groupBy: ['user_id'],
    having: { post_count: { $gt: 5 } },
  },
  { id: 1, role: 'admin' },
);
```

### Subqueries

For more complex queries, you can use subqueries:

```ts [ts]
const usersWithHighRatedPosts = await db.endpoints.data.query(
  'users',
  {
    select: ['id', 'username', 'email'],
    whereExists: {
      table: 'posts',
      on: { 'users.id': 'posts.user_id' },
      where: { rating: { $gt: 4 } },
    },
  },
  { id: 1, role: 'admin' },
);
```

## Create Operations

### Creating a Single Record

To create a new record, use the `data.create` endpoint:

```ts [ts]
const newUser = await db.endpoints.data.create(
  {
    tableName: 'users',
    data: {
      username: 'johndoe',
      email: '<EMAIL>',
      password: 'hashedpassword',
      role: 'user',
    },
  },
  { id: 1, role: 'admin' }, // User context for RLS
);

console.log('Created user:', newUser);
```

### Creating Multiple Records

To create multiple records at once, provide an array of data objects:

```ts [ts]
const newProducts = await db.endpoints.data.create(
  {
    tableName: 'products',
    data: [
      { name: 'Product 1', price: 99.99, category_id: 1 },
      { name: 'Product 2', price: 149.99, category_id: 2 },
      { name: 'Product 3', price: 199.99, category_id: 1 },
    ],
  },
  { id: 1, role: 'admin' },
);

console.log('Created products:', newProducts);
```

## Update Operations

### Updating a Record by ID

To update a record by its ID, use the `data.update` endpoint:

```ts [ts]
const updatedUser = await db.endpoints.data.update(
  {
    tableName: 'users',
    id: 1,
    data: {
      username: 'johndoe_updated',
      role: 'moderator',
    },
  },
  { id: 1, role: 'admin' }, // User context for RLS
);

console.log('Updated user:', updatedUser);
```

### Advanced Updates

For more complex updates, use the `data.advanceUpdate` endpoint:

```ts [ts]
const updatedProducts = await db.endpoints.data.advanceUpdate(
  {
    tableName: 'products',
    filter: {
      category_id: 1,
      price: { $lt: 100 },
    },
    data: {
      discount_percentage: 10,
      on_sale: true,
    },
  },
  { id: 1, role: 'admin' },
);

console.log('Updated products count:', updatedProducts.count);
```

## Delete Operations

### Deleting a Record by ID

To delete a record by its ID, use the `data.delete` endpoint:

```ts [ts]
const deleteResult = await db.endpoints.data.delete(
  {
    tableName: 'users',
    id: 5,
  },
  { id: 1, role: 'admin' }, // User context for RLS
);

console.log('Delete result:', deleteResult);
```

### Advanced Deletes

For more complex deletes, use the `data.advanceDelete` endpoint:

```ts [ts]
const deleteResult = await db.endpoints.data.advanceDelete(
  {
    tableName: 'products',
    filter: {
      category_id: { $in: [4, 5] },
      created_at: { $lt: new Date('2023-01-01').toISOString() },
    },
  },
  { id: 1, role: 'admin' },
);

console.log('Deleted products count:', deleteResult.count);
```

## Transactions

For operations that need to be atomic, you can use transactions:

```ts [ts]
await db.transaction(async (trx) => {
  // Create a user
  const user = await db.endpoints.data.create(
    {
      tableName: 'users',
      data: {
        username: 'jane',
        email: '<EMAIL>',
        password: 'hashedpassword',
      },
    },
    { id: 1, role: 'admin' },
    false, // Not a system operation
    trx, // Pass the transaction
  );

  // Create a profile for the user
  await db.endpoints.data.create(
    {
      tableName: 'profiles',
      data: {
        user_id: user.id,
        bio: 'New user',
        avatar_url: 'https://example.com/avatar.jpg',
      },
    },
    { id: 1, role: 'admin' },
    false,
    trx,
  );
});
```

## System Operations

For administrative operations that should bypass row-level security, you can use the `isSystem` flag:

```ts [ts]
// Create a system user (bypassing RLS)
const systemUser = await db.endpoints.data.create(
  {
    tableName: 'users',
    data: {
      username: 'system',
      email: '<EMAIL>',
      password: 'hashedpassword',
      role: 'system',
    },
  },
  { id: 0, role: 'system' }, // System context
  true, // isSystem flag
);
```

## Query Parameters Reference

Here's a complete reference of query parameters:

| Parameter        | Description                 | Example                                                           |
| ---------------- | --------------------------- | ----------------------------------------------------------------- |
| `select`         | Columns to select           | `['id', 'name', { fn: 'count', args: ['*'], as: 'count' }]`       |
| `where`          | Simple equality conditions  | `{ status: 'active', role: 'user' }`                              |
| `filter`         | Complex filter conditions   | `{ age: { $gt: 18 }, status: { $in: ['active', 'pending'] } }`    |
| `orderBy`        | Sorting criteria            | `[{ column: 'created_at', direction: 'desc' }]`                   |
| `limit`          | Maximum number of records   | `10`                                                              |
| `offset`         | Number of records to skip   | `20`                                                              |
| `join`           | Table joins                 | `[{ table: 'profiles', on: { 'users.id': 'profiles.user_id' } }]` |
| `groupBy`        | Grouping columns            | `['category_id', 'status']`                                       |
| `having`         | Filters for grouped results | `{ count: { $gt: 5 } }`                                           |
| `whereExists`    | Existence subquery          | `{ table: 'orders', on: { 'users.id': 'orders.user_id' } }`       |
| `whereNotExists` | Non-existence subquery      | `{ table: 'orders', on: { 'users.id': 'orders.user_id' } }`       |
| `distinct`       | Return distinct results     | `true`                                                            |

## Filter Operators

ForgeBase Database supports a variety of filter operators:

| Operator      | Description            | Example                                  |
| ------------- | ---------------------- | ---------------------------------------- |
| `$eq`         | Equal                  | `{ age: { $eq: 30 } }`                   |
| `$ne`         | Not equal              | `{ status: { $ne: 'inactive' } }`        |
| `$gt`         | Greater than           | `{ price: { $gt: 100 } }`                |
| `$gte`        | Greater than or equal  | `{ price: { $gte: 100 } }`               |
| `$lt`         | Less than              | `{ price: { $lt: 100 } }`                |
| `$lte`        | Less than or equal     | `{ price: { $lte: 100 } }`               |
| `$in`         | In array               | `{ category_id: { $in: [1, 2, 3] } }`    |
| `$nin`        | Not in array           | `{ category_id: { $nin: [4, 5] } }`      |
| `$like`       | SQL LIKE               | `{ name: { $like: '%phone%' } }`         |
| `$notLike`    | SQL NOT LIKE           | `{ name: { $notLike: '%tablet%' } }`     |
| `$between`    | Between two values     | `{ price: { $between: [100, 500] } }`    |
| `$notBetween` | Not between two values | `{ price: { $notBetween: [100, 500] } }` |
| `$isNull`     | Is NULL                | `{ deleted_at: { $isNull: true } }`      |
| `$isNotNull`  | Is NOT NULL            | `{ email: { $isNotNull: true } }`        |

## Best Practices

1. **Use Transactions**: Wrap related operations in transactions to ensure atomicity.

2. **Limit Result Sets**: Always use limits on queries that could return large result sets.

3. **Select Only Needed Columns**: Only select the columns you need to reduce data transfer.

4. **Use Indexes**: Ensure that columns used in WHERE clauses and joins are indexed.

5. **Consider RLS Impact**: Remember that row-level security will filter results based on the user context.

6. **Validate Input Data**: Validate data before inserting or updating to ensure data integrity.

7. **Handle Errors**: Implement proper error handling for database operations.

## Next Steps

- [Row-Level Security](/database/row-level-security): Learn about implementing fine-grained access control
- [Permissions](/database/permissions): Manage role-based permissions
- [Transactions](/database/transactions): Dive deeper into transaction management
