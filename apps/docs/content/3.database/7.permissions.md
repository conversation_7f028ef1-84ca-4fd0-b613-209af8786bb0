---
title: Permissions
description: Learn how to manage role-based permissions with ForgeBase Database
icon: 'lucide:lock'
---

ForgeBase Database provides a comprehensive permissions system that allows you to control which roles can perform which operations on which tables. This guide covers how to manage permissions in your applications.

## Permission Structure

Permissions in ForgeBase Database are defined at the table level and include:

1. **Operation-specific permissions**: Different permissions for SELECT, INSERT, UPDATE, and DELETE operations
2. **Rule-based access control**: Define rules for who can access what data
3. **Row-level security (RLS)**: Define which rows each role can access

The basic structure of a permission object is:

```ts [ts]
const permissions = {
  operations: {
    SELECT: [
      {
        allow: 'auth', // Only authenticated users
        fieldCheck: {
          field: 'user_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      {
        allow: 'role', // Specific roles
        roles: ['admin', 'moderator'],
      },
      {
        allow: 'public', // Everyone
      },
    ],
    INSERT: [
      {
        allow: 'role',
        roles: ['admin', 'editor'],
      },
    ],
    UPDATE: [
      {
        allow: 'auth',
        fieldCheck: {
          field: 'user_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
    DELETE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
  },
};
```

## Setting Permissions

To set permissions for a table, use the `setPermissions` method:

```ts [ts]
await db.setPermissions('posts', permissions);
```

## Getting Permissions

To get the current permissions for a table:

```ts [ts]
const currentPermissions = await db.getPermissionService().getPermissionsForTable('posts');
console.log(currentPermissions);
```

## Default Permissions

You can set default permissions when initializing the ForgeDatabase instance:

```ts [ts]
const db = new ForgeDatabase({
  db: knexInstance,
  enforceRls: true,
  defaultPermissions: {
    operations: {
      SELECT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
      INSERT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
      UPDATE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
      DELETE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
    },
  },
});
```

These default permissions will be applied to new tables when they are created.

## Permission Examples

### Public Read-Only Table

```ts [ts]
const publicReadOnlyPermissions = {
  operations: {
    SELECT: [
      {
        allow: 'public', // Everyone can read
      },
    ],
    INSERT: [
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
    UPDATE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
    DELETE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
  },
};

await db.setPermissions('public_data', publicReadOnlyPermissions);
```

### User-Owned Data

```ts [ts]
const userOwnedPermissions = {
  operations: {
    SELECT: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'user_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    INSERT: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'auth', // Authenticated users can create
      },
    ],
    UPDATE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'user_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    DELETE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'user_id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
  },
};

await db.setPermissions('user_data', userOwnedPermissions);
```

### Team-Based Access

```ts [ts]
const teamPermissions = {
  operations: {
    SELECT: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'team_id',
          operator: 'in',
          valueType: 'userContext',
          value: 'teams',
        },
      },
    ],
    INSERT: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'customFunction',
        customFunction: 'isTeamAdmin',
      },
    ],
    UPDATE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'customFunction',
        customFunction: 'isTeamAdmin',
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'created_by',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    DELETE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'customFunction',
        customFunction: 'isTeamAdmin',
      },
    ],
  },
};

// Register the custom function
rlsFunctionRegistry.register('isTeamAdmin', async (userContext, row, knex) => {
  if (!knex || !row.team_id) return false;

  const isAdmin = await knex('team_members')
    .where({
      team_id: row.team_id,
      user_id: userContext.userId,
      role: 'admin',
    })
    .first();

  return !!isAdmin;
});

await db.setPermissions('team_projects', teamPermissions);
```

## Automatic Permission Initialization

ForgeBase Database can automatically initialize permissions for all tables in your database. This feature is useful when you want to ensure that all tables have at least basic permissions set.

### Configuration

You can enable automatic permission initialization when creating the ForgeDatabase instance:

```ts [ts]
const db = new ForgeDatabase({
  db: knexInstance,
  enforceRls: true,
  defaultPermissions: {
    operations: {
      SELECT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
      INSERT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
      UPDATE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
      DELETE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
    },
  },
  initializePermissions: true, // Automatically initialize permissions
  permissionReportPath: './permission-report.md', // Optional: save a report
  onPermissionInitComplete: (report) => {
    console.log(`Initialized permissions for ${report.tablesInitialized} tables`);
  },
});
```

This will:

1. Check all tables in the database
2. For tables without permissions, apply the default permissions
3. Generate a report of the initialization process
4. Call the `onPermissionInitComplete` callback with the report

### Manual Initialization

You can also manually trigger permission initialization at any time:

```ts [ts]
// Initialize permissions with default options from config
db.initializePermissions();

// Or specify custom options
db.initializePermissions('./custom-report-path.md', (report) => {
  console.log('Permission initialization completed!');
  console.log(`Tables initialized: ${report.initializedTables.join(', ')}`);
});
```

## Permission Caching

ForgeBase Database uses an LRU cache for permissions to improve performance. The cache is automatically updated when permissions are changed.

## Role Hierarchy

ForgeBase Database does not enforce a role hierarchy by default, but you can implement one in your application logic. For example:

```ts [ts]
function hasRole(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    admin: ['admin', 'manager', 'user', 'anonymous'],
    manager: ['manager', 'user', 'anonymous'],
    user: ['user', 'anonymous'],
    anonymous: ['anonymous'],
  };

  return roleHierarchy[userRole]?.includes(requiredRole) || false;
}
```

## Permission Checking in Your Application

While ForgeBase Database automatically enforces permissions for database operations, you may want to check permissions in your application logic:

```ts [ts]
import { enforcePermissions } from '@the-forgebase/database';

async function canUserPerformOperation(tableName: string, operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE', userContext: { userId: number | string; role: string; [key: string]: any }, row?: any): Promise<boolean> {
  // Get the permission service
  const permissionService = db.getPermissionService();

  // Check permissions
  const result = await enforcePermissions(tableName, operation, userContext, permissionService, row);

  return result.status;
}

// Usage
const canEdit = await canUserPerformOperation('posts', 'UPDATE', { userId: 123, role: 'user' }, { id: 456, user_id: 123, title: 'My Post' });

if (canEdit) {
  // Show edit button
} else {
  // Hide edit button
}
```

## Best Practices

1. **Start Restrictive**: Begin with restrictive permissions and add access as needed.

2. **Use Role-Based Design**: Design your permissions around roles rather than individual users.

3. **Document Your Permission Model**: Keep documentation of your permission model for reference.

4. **Test Permissions**: Test your permissions with different user contexts to ensure they work as expected.

5. **Regular Audits**: Regularly audit your permissions to ensure they align with your security requirements.

6. **Consider Column-Level Permissions**: For sensitive data, consider implementing column-level permissions in your application logic.

7. **Use Transactions**: When updating permissions, use transactions to ensure atomicity.

## Next Steps

- [Row-Level Security](/database/row-level-security): Learn more about implementing fine-grained access control
- [Data Operations](/database/data-operations): Dive deeper into data operations
- [Transactions](/database/transactions): Use transactions for data integrity
