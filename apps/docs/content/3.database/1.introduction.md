---
title: Introduction
description: A flexible, powerful database abstraction layer for ForgeBase with support for multiple database engines, row-level security, and real-time capabilities
icon: 'lucide:door-open'
---

The `@the-forgebase/database` package provides a flexible, powerful database abstraction layer for ForgeBase, with support for multiple database engines, row-level security (RLS), and real-time capabilities.

## Purpose

The database package serves as the foundation for data storage and retrieval in ForgeBase applications. It abstracts away the complexities of working directly with database engines, providing a unified interface for schema management, data operations, and security controls.

## Core Features

### Multiple Database Support

ForgeBase Database supports multiple database engines through its adapter system:

- **SQLite**: Ideal for development, testing, and small to medium applications
- **PostgreSQL**: Powerful, scalable database for production applications
- **LibSQL**: SQLite-compatible database with additional features
  ::alert{type="note" to="https://github.com/ZTL-UwU/shadcn-docs-nuxt" target="\_blank" icon="lucide:link"}
  There are more supported database. checkout the knex docs
  ::

### Row-Level Security (RLS)

Fine-grained access control at the row level allows you to define who can access which rows based on user roles and attributes:

- Define rules using SQL-like conditions
- Apply different rules for different roles
- Automatically filter query results based on user context

### Permission Management

Role-based access control for tables and operations:

- Define permissions for read, create, update, and delete operations
- Assign permissions to roles
- Combine with RLS for comprehensive security

### Schema Management

Powerful tools for managing database schema:

- Create, modify, and delete tables
- Add, modify, and remove columns
- Define relationships between tables
- Create and manage indexes

### Query Builder

Flexible query building capabilities:

- Filter data with complex conditions
- Sort results by multiple columns
- Paginate results with limit and offset
- Select specific columns
- Join related tables

### Real-time Updates

::alert{type="warning" icon="lucide:triangle-alert"}
This is not yet completely finished
::

Optional real-time database changes via WebSockets:

- Broadcast changes to connected clients
- Subscribe to specific tables or queries
- Receive updates when data changes

### Type Safety

Full TypeScript support with type definitions:

- Type-safe database operations
- Intellisense support in modern IDEs
- Compile-time checking of database operations

### Adapter System

::alert{type="warning" icon="lucide:triangle-alert"}
This is not yet completely finished
::
Extensible adapter system for different database engines:

- Abstract away database-specific features
- Consistent API across different databases
- Easy to add support for new database engines

## Installation

:pm-install{name="@the-forgebase/database"}

## Next Steps

- [Features](/database/features): Comprehensive overview of all ForgeBase Database features
- [Getting Started](/database/getting-started): Learn how to set up and use the database package
- [Schema Management](/database/schema-management): Manage database schema
- [Data Operations](/database/data-operations): Perform CRUD operations
- [Row-Level Security](/database/row-level-security): Implement fine-grained access control
- [Permissions](/database/permissions): Manage role-based permissions
- [Real-time Updates](/database/real-time): Enable real-time database updates
- [Adapters](/database/adapters): Work with different database engines
- [Transactions](/database/transactions): Use transactions for data integrity
- [API Reference](/database/api-reference): Detailed API documentation
- [Complete Integration](/database/complete-integration): Use ForgeBase Database with frontend SDK, REST API, and custom frameworks
