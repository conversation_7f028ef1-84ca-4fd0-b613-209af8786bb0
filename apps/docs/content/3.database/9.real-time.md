---
title: Real-time Updates
description: Learn how to enable and use real-time database updates with ForgeBase Database
icon: 'lucide:database-zap'
---

::alert{type="danger" icon="lucide:circle-x"}
Some Implementation and Docs are still is WIP
::

ForgeBase Database provides real-time update capabilities, allowing clients to receive notifications when data changes. This guide covers how to enable and use real-time updates in your applications.

## Enabling Real-time Updates

To enable real-time updates, set the `realtime` option to `true` when initializing the ForgeDatabase instance:

::alert{type="success" icon="lucide:lightbulb"}
This is completed and working
::

```ts [ts]
import { ForgeDatabase } from '@the-forgebase/database';
import knex from 'knex';

const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

const db = new ForgeDatabase({
  db: knexInstance,
  realtime: true, // Enable real-time updates
  websocketPort: 8080, // Optional, defaults to 8080
  realtimeAdapter: 'websocket', // Optional, defaults to 'sse'
});
```

## How Real-time Updates Work

When real-time updates are enabled, ForgeBase Database sets up a WebSocket server that broadcasts changes to connected clients. The process works as follows:

1. **Database Changes**: When data is created, updated, or deleted through the ForgeDatabase API
2. **Change Detection**: The database hooks detect the changes
3. **Broadcasting**: The changes are broadcast to connected clients via WebSocket
4. **Client Reception**: Clients receive the changes and can update their UI accordingly

## WebSocket Server

The WebSocket server is automatically started when you initialize ForgeDatabase with `realtime: true`. By default, it listens on port 8080, but you can specify a different port using the `websocketPort` option.

The server uses the `uWebSockets.js` library for high-performance WebSocket communication.

## Client Connection

::alert{type="success" icon="lucide:lightbulb"}
This is completed and working
::
Clients can connect to the WebSocket server to receive real-time updates. Here's an example using the browser WebSocket API:

```js [js]
// Connect to the WebSocket server
const socket = new WebSocket('ws://localhost:8080');

// Handle connection open
socket.addEventListener('open', (event) => {
  console.log('Connected to ForgeBase real-time server');

  // Subscribe to a table
  socket.send(
    JSON.stringify({
      type: 'subscribe',
      table: 'posts',
    }),
  );
});

// Handle messages
socket.addEventListener('message', (event) => {
  const data = JSON.parse(event.data);
  console.log('Received update:', data);

  // Update UI based on the change
  if (data.type === 'create') {
    addNewPost(data.record);
  } else if (data.type === 'update') {
    updatePost(data.record);
  } else if (data.type === 'delete') {
    removePost(data.id);
  }
});

// Handle errors
socket.addEventListener('error', (event) => {
  console.error('WebSocket error:', event);
});

// Handle connection close
socket.addEventListener('close', (event) => {
  console.log('Disconnected from ForgeBase real-time server');
});
```

## Message Format

::alert{type="success" icon="lucide:lightbulb"}
This is completed and working
::
The WebSocket server sends messages in the following format:

### Create Event

```json [json]
{
  "type": "create",
  "table": "posts",
  "record": {
    "id": 123,
    "title": "New Post",
    "content": "Post content",
    "user_id": 456,
    "created_at": "2023-07-15T10:30:00Z"
  }
}
```

### Update Event

```json [json]
{
  "type": "update",
  "table": "posts",
  "record": {
    "id": 123,
    "title": "Updated Post",
    "content": "Updated content",
    "user_id": 456,
    "created_at": "2023-07-15T10:30:00Z",
    "updated_at": "2023-07-15T11:45:00Z"
  }
}
```

### Delete Event

```json [json]
{
  "type": "delete",
  "table": "posts",
  "id": 123
}
```

## Subscribing to Tables

::alert{type="success" icon="lucide:lightbulb"}
This is completed and working
::
Clients can subscribe to specific tables to receive updates only for those tables:

```js [js]
// Subscribe to the 'posts' table
socket.send(
  JSON.stringify({
    type: 'subscribe',
    table: 'posts',
  }),
);

// Subscribe to the 'comments' table
socket.send(
  JSON.stringify({
    type: 'subscribe',
    table: 'comments',
  }),
);
```

## Unsubscribing from Tables

::alert{type="success" icon="lucide:lightbulb"}
This is completed and working
::
Clients can unsubscribe from tables when they no longer need updates:

```javascript [js]
// Unsubscribe from the 'comments' table
socket.send(
  JSON.stringify({
    type: 'unsubscribe',
    table: 'comments',
  }),
);
```

## Authentication and Authorization

::alert{type="success" icon="lucide:lightbulb"}
Implementation completed
::
::alert{type="info" icon="lucide:info"}
Docs is WIP
::

## Row-Level Security for Real-time Updates

::alert{type="success" icon="lucide:lightbulb"}
Implementation completed
::
::alert{type="info" icon="lucide:info"}
Docs is WIP
::

## Using with ForgeBase SDK

::alert{type="danger" icon="lucide:circle-x"}
Implementation is WIP, this is just a proposed interface
::
The ForgeBase SDK provides built-in support for real-time updates. Here's an example of using real-time updates with the SDK:

```typescript [ts]
import { DatabaseSDK } from '@the-forgebase/sdk/client';

// Initialize the SDK
const db = new DatabaseSDK('http://localhost:3000', {
  realtime: true, // Enable real-time updates
  websocketUrl: 'ws://localhost:8080', // WebSocket server URL
});

// Subscribe to the 'posts' table
const unsubscribe = db.table('posts').subscribe((event) => {
  console.log('Received update:', event);

  // Update UI based on the event
  if (event.type === 'create') {
    addNewPost(event.record);
  } else if (event.type === 'update') {
    updatePost(event.record);
  } else if (event.type === 'delete') {
    removePost(event.id);
  }
});

// Later, unsubscribe when no longer needed
unsubscribe();
```

## Performance Considerations

Real-time updates can impact server performance, especially with a large number of connected clients or frequent data changes. Here are some tips to optimize performance:

1. **Selective Subscriptions**: Subscribe only to the tables you need.

2. **Batching Updates**: The server batches updates to reduce the number of messages sent.

3. **Connection Limits**: Consider implementing connection limits to prevent overload.

4. **Scaling**: For high-traffic applications, consider using a dedicated WebSocket server or a message broker like Redis.

5. **Monitoring**: Monitor WebSocket server performance and resource usage.

## Best Practices

1. **Implement Authentication**: Always implement authentication for WebSocket connections in production.

2. **Handle Reconnection**: Implement reconnection logic on the client side to handle network interruptions.

3. **Error Handling**: Implement proper error handling for WebSocket connections.

4. **Testing**: Test real-time updates with different scenarios, including network interruptions.

5. **Documentation**: Document the real-time update capabilities for client developers.

6. **Security**: Be mindful of security implications, especially when broadcasting sensitive data.

7. **Fallback Mechanism**: Implement a fallback mechanism for clients that don't support WebSockets.

## Next Steps

- [SDK Integration](/sdk/real-time): Learn how to use real-time updates with the ForgeBase SDK
- [Authentication](/auth/introduction): Implement authentication for your application
- [Row-Level Security](/database/row-level-security): Learn more about row-level security
