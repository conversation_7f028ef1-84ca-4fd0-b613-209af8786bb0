---
title: Complete Integration
description: Learn how to use ForgeBase Database with the frontend SDK, REST API, and custom frameworks
icon: 'lucide:handshake'
---

This guide demonstrates how to use ForgeBase Database in a complete application stack, covering backend setup, frontend integration, REST API usage, and custom framework integration.

## Overview

ForgeBase provides multiple ways to integrate your database into your application:

1. **API Package**: The `@the-forgebase/api` package that exposes your database through a REST API
2. **Frontend SDK**: The `@the-forgebase/sdk` package that provides a type-safe client for interacting with your database
3. **Direct REST API**: Access your database directly through REST API endpoints
4. **Custom Framework Integration**: Use the database package directly in frameworks like Hono, Express, or Next.js

This flexibility allows you to:

- Build type-safe applications with end-to-end TypeScript support
- Leverage the power of ForgeBase Database's row-level security on the backend
- Use a fluent query builder on the frontend for complex data operations
- Implement real-time updates for reactive applications
- Integrate with your preferred backend framework

## Backend Setup

First, let's set up the backend API that will expose your ForgeBase Database.

### Installing Dependencies

:pm-install{name="@the-forgebase/api @the-forgebase/database knex"}

You'll also need to install a database driver based on your preferred database:

::alert{type="note" to="https://github.com/ZTL-UwU/shadcn-docs-nuxt" target="\_blank" icon="lucide:link"}
There are more supported database. checkout the knex docs
::

####

::tabs{variant="line"}
::div{label="SQLite" icon="lucide:database"}
:pm-install{name="better-sqlite3"}
::

::div{label="PostgreSQL" icon="lucide:database"}
:pm-install{name="pg"}
::

::div{label="LibSQL" icon="lucide:database"}
:pm-install{name="@libsql/client"}
::
::

### Setting Up the API

Here's a basic example of setting up an API with Express:

```typescript [ts]
// server.ts
import express from 'express';
import cors from 'cors';
import { forgeApi } from '@the-forgebase/api';
import knex from 'knex';

// Create a Knex instance
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Initialize the ForgeBase API
const api = forgeApi({
  prefix: '/api',
  services: {
    db: {
      provider: 'sqlite',
      config: {
        db: knexInstance,
        enforceRls: true,
        realtime: true,
        websocketPort: 8080,
      },
    },
  },
});

// Create Express app
const app = express();

// Configure middleware
app.use(
  cors({
    origin: 'http://localhost:5173', // Your frontend URL
    credentials: true,
  }),
);
app.use(express.json());

// Mount the ForgeBase API
app.use('/api', api.handle);

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

### Setting Up Database Schema and Permissions

Before your API can be used, you need to set up your database schema and permissions:

```typescript [ts]
// setup-database.ts
import { ForgeDatabase } from '@the-forgebase/database';
import knex from 'knex';

async function setupDatabase() {
  // Create a Knex instance
  const knexInstance = knex({
    client: 'sqlite3',
    connection: {
      filename: './mydb.sqlite',
    },
    useNullAsDefault: true,
  });

  // Initialize ForgeDatabase
  const db = new ForgeDatabase({
    db: knexInstance,
    enforceRls: true,
  });

  // Create users table
  await db.endpoints.schema.create({
    tableName: 'users',
    columns: [
      { name: 'id', type: 'increments', primary: true },
      { name: 'username', type: 'string', unique: true, nullable: false },
      { name: 'email', type: 'string', unique: true, nullable: false },
      { name: 'role', type: 'string', defaultValue: 'user' },
      { name: 'created_at', type: 'timestamp', defaultToNow: true },
    ],
  });

  // Create posts table
  await db.endpoints.schema.create({
    tableName: 'posts',
    columns: [
      { name: 'id', type: 'increments', primary: true },
      { name: 'title', type: 'string', nullable: false },
      { name: 'content', type: 'text' },
      { name: 'author_id', type: 'integer', references: { table: 'users', column: 'id' } },
      { name: 'is_published', type: 'boolean', defaultValue: false },
      { name: 'created_at', type: 'timestamp', defaultToNow: true },
    ],
  });

  // Set permissions for users table
  await db.setPermissions('users', {
    operations: {
      SELECT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
        {
          allow: 'auth',
          fieldCheck: {
            field: 'id',
            operator: '===',
            valueType: 'userContext',
            value: 'userId',
          },
        },
      ],
      INSERT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
      UPDATE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
        {
          allow: 'auth',
          fieldCheck: {
            field: 'id',
            operator: '===',
            valueType: 'userContext',
            value: 'userId',
          },
        },
      ],
      DELETE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
    },
  });

  // Set permissions for posts table
  await db.setPermissions('posts', {
    operations: {
      SELECT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
        {
          allow: 'customSql',
          customSql: `
            SELECT 1 WHERE
              is_published = true
              OR author_id = :userId
          `,
        },
      ],
      INSERT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
        {
          allow: 'auth', // Any authenticated user can create posts
        },
      ],
      UPDATE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
        {
          allow: 'auth',
          fieldCheck: {
            field: 'author_id',
            operator: '===',
            valueType: 'userContext',
            value: 'userId',
          },
        },
      ],
      DELETE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
        {
          allow: 'auth',
          fieldCheck: {
            field: 'author_id',
            operator: '===',
            valueType: 'userContext',
            value: 'userId',
          },
        },
      ],
    },
  });

  // Insert some sample data
  await db.endpoints.data.create(
    {
      tableName: 'users',
      data: {
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
      },
    },
    { userId: 0, role: 'system' },
    true,
  );

  await db.endpoints.data.create(
    {
      tableName: 'users',
      data: {
        username: 'user1',
        email: '<EMAIL>',
        role: 'user',
      },
    },
    { userId: 0, role: 'system' },
    true,
  );

  console.log('Database setup complete!');
  await knexInstance.destroy();
}

setupDatabase().catch(console.error);
```

## Frontend Setup

Now, let's set up the frontend to interact with your ForgeBase Database API.

### Installing Dependencies

:pm-install{name="@the-forgebase/sdk axios"}

### Basic Usage

Here's a simple example of using the SDK in a frontend application:

```typescript [ts]
// api.ts
import { DatabaseSDK } from '@the-forgebase/sdk/client';

// Initialize the SDK with your API URL
export const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  axiosConfig: {
    withCredentials: true, // Important for auth cookies
  },
});
```

Now you can use this SDK instance throughout your application:

```typescript [ts]
// users.ts
import { db } from './api';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  created_at: string;
}

// Get all users (admin only due to RLS)
export async function getUsers() {
  try {
    const response = await db.table<User>('users').query();
    return response.records || [];
  } catch (error) {
    console.error('Error fetching users:', error);
    return [];
  }
}

// Get current user by ID
export async function getUserById(id: number) {
  try {
    const response = await db.table<User>('users').where('id', id).query();
    return response.records?.[0];
  } catch (error) {
    console.error(`Error fetching user ${id}:`, error);
    return null;
  }
}

// Update user profile
export async function updateUserProfile(id: number, data: Partial<User>) {
  try {
    const response = await db.table<User>('users').update(id, data);
    return response.records?.[0];
  } catch (error) {
    console.error(`Error updating user ${id}:`, error);
    throw error;
  }
}
```

```typescript [ts]
// posts.ts
import { db } from './api';

interface Post {
  id: number;
  title: string;
  content: string;
  author_id: number;
  is_published: boolean;
  created_at: string;
}

// Get all published posts
export async function getPublishedPosts() {
  try {
    const response = await db.table<Post>('posts').where('is_published', true).orderBy('created_at', 'desc').query();
    return response.records || [];
  } catch (error) {
    console.error('Error fetching published posts:', error);
    return [];
  }
}

// Get user's posts (published and drafts)
export async function getUserPosts(userId: number) {
  try {
    const response = await db.table<Post>('posts').where('author_id', userId).orderBy('created_at', 'desc').query();
    return response.records || [];
  } catch (error) {
    console.error(`Error fetching posts for user ${userId}:`, error);
    return [];
  }
}

// Create a new post
export async function createPost(data: Omit<Post, 'id' | 'created_at'>) {
  try {
    const response = await db.table<Post>('posts').create(data);
    return response.records?.[0];
  } catch (error) {
    console.error('Error creating post:', error);
    throw error;
  }
}

// Update a post
export async function updatePost(id: number, data: Partial<Post>) {
  try {
    const response = await db.table<Post>('posts').update(id, data);
    return response.records?.[0];
  } catch (error) {
    console.error(`Error updating post ${id}:`, error);
    throw error;
  }
}

// Delete a post
export async function deletePost(id: number) {
  try {
    await db.table<Post>('posts').delete(id);
    return true;
  } catch (error) {
    console.error(`Error deleting post ${id}:`, error);
    throw error;
  }
}
```

### Integration with React

Here's how you can use the SDK in a React application:

```tsx [tsx]
// UserProfile.tsx
import React, { useEffect, useState } from 'react';
import { getUserById, updateUserProfile } from './users';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
}

interface UserProfileProps {
  userId: number;
}

const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState<Partial<User>>({});

  useEffect(() => {
    async function fetchUser() {
      try {
        setLoading(true);
        const userData = await getUserById(userId);
        setUser(userData);
        setFormData(userData || {});
      } catch (err) {
        setError('Failed to load user profile');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchUser();
  }, [userId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const updatedUser = await updateUserProfile(userId, formData);
      setUser(updatedUser);
      setEditing(false);
      setError(null);
    } catch (err) {
      setError('Failed to update profile');
      console.error(err);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div className="user-profile">
      {editing ? (
        <form onSubmit={handleSubmit}>
          <div>
            <label htmlFor="username">Username:</label>
            <input type="text" id="username" name="username" value={formData.username || ''} onChange={handleChange} />
          </div>
          <div>
            <label htmlFor="email">Email:</label>
            <input type="email" id="email" name="email" value={formData.email || ''} onChange={handleChange} />
          </div>
          <div>
            <button type="submit">Save</button>
            <button type="button" onClick={() => setEditing(false)}>
              Cancel
            </button>
          </div>
        </form>
      ) : (
        <div>
          <h2>{user.username}</h2>
          <p>Email: {user.email}</p>
          <p>Role: {user.role}</p>
          <button onClick={() => setEditing(true)}>Edit Profile</button>
        </div>
      )}
    </div>
  );
};

export default UserProfile;
```

```tsx [tsx]
// PostsList.tsx
import React, { useEffect, useState } from 'react';
import { getPublishedPosts, getUserPosts, deletePost } from './posts';

interface Post {
  id: number;
  title: string;
  content: string;
  author_id: number;
  is_published: boolean;
  created_at: string;
}

interface PostsListProps {
  userId?: number; // Optional: if provided, shows only user's posts
}

const PostsList: React.FC<PostsListProps> = ({ userId }) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPosts() {
      try {
        setLoading(true);
        const postsData = userId ? await getUserPosts(userId) : await getPublishedPosts();
        setPosts(postsData);
      } catch (err) {
        setError('Failed to load posts');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchPosts();
  }, [userId]);

  const handleDelete = async (postId: number) => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      try {
        await deletePost(postId);
        setPosts((prevPosts) => prevPosts.filter((post) => post.id !== postId));
      } catch (err) {
        setError('Failed to delete post');
        console.error(err);
      }
    }
  };

  if (loading) return <div>Loading posts...</div>;
  if (error) return <div>Error: {error}</div>;
  if (posts.length === 0) return <div>No posts found</div>;

  return (
    <div className="posts-list">
      {posts.map((post) => (
        <div key={post.id} className="post-card">
          <h3>{post.title}</h3>
          <p>{post.content.substring(0, 100)}...</p>
          <div className="post-meta">
            <span>{new Date(post.created_at).toLocaleDateString()}</span>
            {userId && (
              <div className="post-actions">
                <button onClick={() => handleDelete(post.id)}>Delete</button>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default PostsList;
```

### Real-time Updates

::alert{type="danger" icon="lucide:circle-x"}
Implementation is WIP, this is just a proposed interface
::
ForgeBase Database supports real-time updates via WebSockets. Here's how to use them with the SDK:

```typescript [ts]
// api.ts
import { DatabaseSDK } from '@the-forgebase/sdk/client';

export const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  axiosConfig: {
    withCredentials: true,
  },
  // Enable real-time updates
  realtimeConfig: {
    enabled: true,
    websocketUrl: 'ws://localhost:8080',
  },
});
```

```tsx [tsx]
// RealtimePosts.tsx
import React, { useEffect, useState } from 'react';
import { db } from './api';

interface Post {
  id: number;
  title: string;
  content: string;
  author_id: number;
  is_published: boolean;
  created_at: string;
}

const RealtimePosts: React.FC = () => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPosts() {
      try {
        setLoading(true);
        const response = await db.table<Post>('posts').where('is_published', true).orderBy('created_at', 'desc').query();
        setPosts(response.records || []);
      } catch (err) {
        setError('Failed to load posts');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchPosts();

    // Subscribe to real-time updates
    const unsubscribe = db.table<Post>('posts').subscribe((event) => {
      if (event.type === 'create' && event.record.is_published) {
        setPosts((prevPosts) => [event.record, ...prevPosts]);
      } else if (event.type === 'update') {
        setPosts((prevPosts) => prevPosts.map((post) => (post.id === event.record.id ? event.record : post)));
      } else if (event.type === 'delete') {
        setPosts((prevPosts) => prevPosts.filter((post) => post.id !== event.id));
      }
    });

    // Cleanup subscription on unmount
    return () => {
      unsubscribe();
    };
  }, []);

  if (loading) return <div>Loading posts...</div>;
  if (error) return <div>Error: {error}</div>;
  if (posts.length === 0) return <div>No posts found</div>;

  return (
    <div className="posts-list">
      <h2>Real-time Posts</h2>
      {posts.map((post) => (
        <div key={post.id} className="post-card">
          <h3>{post.title}</h3>
          <p>{post.content.substring(0, 100)}...</p>
          <div className="post-meta">
            <span>{new Date(post.created_at).toLocaleDateString()}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default RealtimePosts;
```

## Authentication Integration

ForgeBase Database works seamlessly with ForgeBase Auth. Here's how to integrate them:

```typescript [ts]
// api.ts
import { DatabaseSDK } from '@the-forgebase/sdk/client';
import { ForgebaseWebAuth } from '@the-forgebase/web-auth';

// Initialize auth
const auth = new ForgebaseWebAuth({
  apiUrl: 'http://localhost:3000/api',
});

// Option 1: Create a database SDK with the auth axios instance
export const db = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  axiosInstance: auth.api,
});

// Option 2: Create a database SDK with auth interceptors
const authInterceptors = auth.getAuthInterceptors();
export const db2 = new DatabaseSDK({
  baseUrl: 'http://localhost:3000/api',
  axiosConfig: { timeout: 5000 },
  authInterceptors,
});
```

## Advanced Query Examples

The SDK provides a powerful query builder for complex operations:

```typescript [ts]
// Advanced filtering
const activeAdminUsers = await db.table<User>('users').where('status', 'active').where('role', 'admin').query();

// Pagination
const paginatedPosts = await db.table<Post>('posts').where('is_published', true).orderBy('created_at', 'desc').limit(10).offset(0).query();

// Joins with related data
const postsWithAuthors = await db.table<Post>('posts').select('posts.*', 'users.username as author_name').join('users', 'posts.author_id', 'users.id').where('posts.is_published', true).orderBy('posts.created_at', 'desc').query();

// Aggregations
const postStats = await db.table<Post>('posts').groupBy('author_id').count('id', 'post_count').avg('likes', 'avg_likes').having('post_count', '>', 5).query();

// Complex filtering
const popularRecentPosts = await db
  .table<Post>('posts')
  .where('is_published', true)
  .where('created_at', '>=', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
  .where('likes', '>=', 10)
  .orderBy('likes', 'desc')
  .limit(5)
  .query();
```

## Supported Databases

ForgeBase Database supports all databases supported by Knex.js:

- **SQLite**: Great for development, testing, and small applications
- **PostgreSQL**: Powerful, scalable database for production applications
- **MySQL/MariaDB**: Popular open-source databases
- **Oracle**: Enterprise-grade database
- **MSSQL**: Microsoft SQL Server
- **LibSQL**: SQLite-compatible database with additional features

To use a different database, simply change the Knex configuration:

::alert{type="note" to="https://github.com/ZTL-UwU/shadcn-docs-nuxt" target="\_blank" icon="lucide:link"}
checkout the knex docs
::

###

::tabs{variant="line"}
::div{label="MySQL"}

```typescript [ts]
// MySQL
const knexInstance = knex({
  client: 'mysql2',
  connection: {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'password',
    database: 'mydb',
  },
});
```

::

::div{label="MSSQL"}

```typescript [ts]
// MSSQL
const knexInstance = knex({
  client: 'mssql',
  connection: {
    server: 'localhost',
    user: 'sa',
    password: 'password',
    database: 'mydb',
  },
});
```

::

::div{label="Oracle"}

```typescript [ts]
// Oracle
const knexInstance = knex({
  client: 'oracledb',
  connection: {
    host: 'localhost',
    user: 'system',
    password: 'password',
    database: 'XE',
  },
});
```

::
::

## Best Practices

1. **Type Safety**: Define TypeScript interfaces for your database tables to get full type safety.

2. **Error Handling**: Always implement proper error handling for database operations.

3. **Pagination**: Use pagination for large datasets to improve performance.

4. **Security**: Leverage row-level security on the backend to enforce access control.

5. **Caching**: Consider implementing caching for frequently accessed data.

6. **Transactions**: Use transactions for operations that need to be atomic.

7. **Validation**: Validate data on both the frontend and backend.

8. **Testing**: Write tests for your database operations to ensure they work as expected.

## REST API Usage

If you prefer to interact with your ForgeBase Database directly through REST API endpoints, you can do so without using the SDK. Here are examples of common operations:

### Schema Management

#### Create a Table

```http [http]
POST /api/schema
Content-Type: application/json

{
  "action": "create",
  "tableName": "users",
  "columns": [
    { "name": "id", "type": "increments", "primary": true },
    { "name": "name", "type": "string" },
    { "name": "email", "type": "string", "unique": true }
  ]
}
```

#### Modify a Table

```http [http]
POST /api/schema
Content-Type: application/json

{
  "action": "modify",
  "tableName": "users",
  "addColumns": [
    { "name": "phone", "type": "string", "nullable": true }
  ]
}
```

#### Delete a Table

```http [http]
POST /api/schema
Content-Type: application/json

{
  "action": "delete",
  "tableName": "users"
}
```

### Data Operations

#### Query Records

```http [http]
GET /api/data/users?filter={"name":"John"}&limit=10&offset=0
```

You can use various query parameters:

- `select`: Comma-separated list of columns to return
- `filter`: JSON object with filter conditions
- `orderBy`: JSON array of ordering criteria
- `limit`: Maximum number of records to return
- `offset`: Number of records to skip

#### Insert Record

```http [http]
POST /api/data/users
Content-Type: application/json

{
  "data": {
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

#### Update Record

```http [http]
PUT /api/data/users/1
Content-Type: application/json

{
  "data": {
    "name": "John Smith",
    "email": "<EMAIL>"
  }
}
```

#### Delete Record

```http [http]
DELETE /api/data/users/1
```

### Permission Management

#### Get Table Permissions

```http [http]
GET /api/permissions/users
```

#### Set Table Permissions

```http [http]
POST /api/permissions/users
Content-Type: application/json

{
  "operations": {
    "SELECT": [
      { "allow": "public" }
    ],
    "INSERT": [
      { "allow": "auth" }
    ],
    "UPDATE": [
      { "allow": "labels", "labels": ["admin"] }
    ],
    "DELETE": [
      { "allow": "teams", "teams": ["moderators"] }
    ]
  }
}
```

#### Field-Based Permission Example

```http [http]
POST /api/permissions/posts
Content-Type: application/json

{
  "operations": {
    "SELECT": [
      {
        "allow": "fieldCheck",
        "fieldCheck": {
          "field": "authorId",
          "operator": "===",
          "valueType": "userContext",
          "value": "userId"
        }
      }
    ]
  }
}
```

#### Delete Table Permissions

```http [http]
DELETE /api/permissions/users
```

## Custom Framework Integration

If you prefer to use the ForgeBase Database directly in your own framework without the API package, you can do so. Here's an example using Hono, a lightweight web framework:

```typescript [ts]
// server.ts
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { ForgeDatabase } from '@the-forgebase/database';
import knex from 'knex';

// Create a Knex instance
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});

// Initialize ForgeDatabase
const db = new ForgeDatabase({
  db: knexInstance,
  enforceRls: true,
});

// Create Hono app
const app = new Hono();

// Configure middleware
app.use(
  cors({
    origin: 'http://localhost:5173', // Your frontend URL
    credentials: true,
  }),
);

// Schema endpoints
app.post('/schema', async (c) => {
  try {
    const body = await c.req.json();
    const { action, tableName, columns, addColumns, dropColumns, modifyColumns } = body;

    if (action === 'create') {
      const result = await db.endpoints.schema.create({
        tableName,
        columns,
      });
      return c.json(result);
    } else if (action === 'modify') {
      const result = await db.endpoints.schema.modify({
        tableName,
        addColumns,
        dropColumns,
        modifyColumns,
      });
      return c.json(result);
    } else if (action === 'delete') {
      const result = await db.endpoints.schema.delete(tableName);
      return c.json(result);
    } else {
      return c.json({ error: 'Invalid action' }, 400);
    }
  } catch (error) {
    console.error('Schema error:', error);
    return c.json({ error: error.message }, 500);
  }
});

// Data endpoints
app.get('/data/:tableName', async (c) => {
  try {
    const tableName = c.req.param('tableName');
    const url = new URL(c.req.url);

    // Parse query parameters
    const select = url.searchParams.get('select')?.split(',') || ['*'];
    const filterStr = url.searchParams.get('filter');
    const filter = filterStr ? JSON.parse(filterStr) : {};
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const orderByStr = url.searchParams.get('orderBy');
    const orderBy = orderByStr ? JSON.parse(orderByStr) : [];

    // Get user context from auth header or session
    const userContext = getUserContextFromRequest(c.req);

    const result = await db.endpoints.data.query(
      tableName,
      {
        select,
        filter,
        limit,
        offset,
        orderBy,
      },
      userContext,
    );

    return c.json({ records: result });
  } catch (error) {
    console.error('Query error:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.post('/data/:tableName', async (c) => {
  try {
    const tableName = c.req.param('tableName');
    const body = await c.req.json();
    const { data } = body;

    // Get user context from auth header or session
    const userContext = getUserContextFromRequest(c.req);

    const result = await db.endpoints.data.create(
      {
        tableName,
        data,
      },
      userContext,
    );

    return c.json({ records: [result] });
  } catch (error) {
    console.error('Create error:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.put('/data/:tableName/:id', async (c) => {
  try {
    const tableName = c.req.param('tableName');
    const id = c.req.param('id');
    const body = await c.req.json();
    const { data } = body;

    // Get user context from auth header or session
    const userContext = getUserContextFromRequest(c.req);

    const result = await db.endpoints.data.update(
      {
        tableName,
        id,
        data,
      },
      userContext,
    );

    return c.json({ records: [result] });
  } catch (error) {
    console.error('Update error:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.delete('/data/:tableName/:id', async (c) => {
  try {
    const tableName = c.req.param('tableName');
    const id = c.req.param('id');

    // Get user context from auth header or session
    const userContext = getUserContextFromRequest(c.req);

    await db.endpoints.data.delete(
      {
        tableName,
        id,
      },
      userContext,
    );

    return c.json({ success: true });
  } catch (error) {
    console.error('Delete error:', error);
    return c.json({ error: error.message }, 500);
  }
});

// Permission endpoints
app.get('/permissions/:tableName', async (c) => {
  try {
    const tableName = c.req.param('tableName');
    const permissions = await db.getPermissionService().getPermissionsForTable(tableName);
    return c.json(permissions);
  } catch (error) {
    console.error('Get permissions error:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.post('/permissions/:tableName', async (c) => {
  try {
    const tableName = c.req.param('tableName');
    const permissions = await c.req.json();

    await db.setPermissions(tableName, permissions);

    return c.json({ success: true });
  } catch (error) {
    console.error('Set permissions error:', error);
    return c.json({ error: error.message }, 500);
  }
});

app.delete('/permissions/:tableName', async (c) => {
  try {
    const tableName = c.req.param('tableName');
    await db.getPermissionService().deletePermissionsForTable(tableName);
    return c.json({ success: true });
  } catch (error) {
    console.error('Delete permissions error:', error);
    return c.json({ error: error.message }, 500);
  }
});

// Helper function to extract user context from request
function getUserContextFromRequest(req) {
  // In a real app, you would extract this from JWT, session, etc.
  // This is just a placeholder
  return {
    userId: 1,
    role: 'admin',
    teams: ['team1'],
    labels: ['admin'],
  };
}

// Start the server
const port = 3000;
console.log(`Server is running on port ${port}`);

export default {
  port,
  fetch: app.fetch,
};
```

This example shows how to create a basic REST API for your ForgeBase Database using Hono. You can adapt this approach to other frameworks like Express, Fastify, or Next.js API routes.

## Next Steps

- [API Integration](/api/introduction): Learn more about the ForgeBase API package
- [Authentication](/auth/introduction): Implement authentication for your application
- [Row-Level Security](/database/row-level-security): Explore advanced RLS scenarios
- [Real-time Updates](/database/real-time): Enable real-time database updates
