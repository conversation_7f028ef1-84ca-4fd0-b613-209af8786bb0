---
title: Getting Started
description: Learn how to set up and use the ForgeBase Database package
icon: 'lucide:rocket'
---

This guide will help you get started with the ForgeBase Database package, from installation to basic usage.

## Prerequisites

- Node.js (LTS version recommended) or bun or deno
- npm, yarn, or pnpm
- Basic knowledge of TypeScript and database concepts

## Installation

Install the package using your preferred package manager:

:pm-install{name="@the-forgebase/database"}

You'll also need to install a database driver based on your preferred database:

::alert{type="note" to="https://github.com/ZTL-UwU/shadcn-docs-nuxt" target="\_blank" icon="lucide:link"}
There are more supported database. checkout the knex docs
::

####

::tabs{variant="line"}
::div{label="SQLite" icon="lucide:database"}
:pm-install{name="better-sqlite3"}
::

::div{label="PostgreSQL" icon="lucide:database"}
:pm-install{name="pg"}
::

::div{label="LibSQL" icon="lucide:database"}
:pm-install{name="@libsql/client"}
::
::

## Basic Setup

### Initialize the Database

First, create a Knex instance and initialize the ForgeDatabase:

::tabs{variant="line"}
::div{label="SQLite" icon="lucide:database"}

```ts [ts]
import { ForgeDatabase } from '@the-forgebase/database';
import knex from 'knex';

// Create a Knex instance for SQLite
const knexInstance = knex({
  client: 'sqlite3',
  connection: {
    filename: './mydb.sqlite',
  },
  useNullAsDefault: true,
});
```

::

::div{label="PostgreSQL" icon="lucide:database"}

```ts [ts]
const knexInstance = knex({
  client: 'pg',
  connection: {
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'password',
    database: 'mydb',
  },
});
```

::
::

```ts [ts] {1}
// Initialize ForgeDatabase
const db = new ForgeDatabase({
  db: knexInstance,
  enforceRls: true, // Enable row-level security
  realtime: false, // Disable real-time updates for now
});
```

### Create Your First Table

Now, let's create a simple users table:

```ts [ts]
await db.endpoints.schema.create({
  tableName: 'users',
  columns: [
    { name: 'id', type: 'increments', primary: true },
    { name: 'username', type: 'string', unique: true, nullable: false },
    { name: 'email', type: 'string', unique: true, nullable: false },
    { name: 'password', type: 'string', nullable: false },
    { name: 'role', type: 'string', defaultValue: 'user' },
    { name: 'created_at', type: 'timestamp', defaultToNow: true },
  ],
});
```

### Set Up Permissions

Define permissions for the users table:

```ts [ts]
await db.setPermissions('users', {
  operations: {
    SELECT: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    INSERT: [
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
    UPDATE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
      {
        allow: 'auth',
        fieldCheck: {
          field: 'id',
          operator: '===',
          valueType: 'userContext',
          value: 'userId',
        },
      },
    ],
    DELETE: [
      {
        allow: 'role',
        roles: ['admin'],
      },
    ],
  },
});
```

### Insert Data

Let's insert some data into the users table:

```ts [ts]
// Create an admin user
await db.endpoints.data.create(
  {
    tableName: 'users',
    data: {
      username: 'admin',
      email: '<EMAIL>',
      password: 'hashedpassword', // In a real app, hash this password
      role: 'admin',
    },
  },
  { userId: 0, role: 'system' }, // System context bypasses RLS
  true, // isSystem flag to bypass RLS
);

// Create a regular user
await db.endpoints.data.create(
  {
    tableName: 'users',
    data: {
      username: 'user1',
      email: '<EMAIL>',
      password: 'hashedpassword', // In a real app, hash this password
      role: 'user',
    },
  },
  { userId: 1, role: 'admin' }, // Admin context
);
```

### Query Data

Now, let's query the data:

```ts [ts]
// Query as admin (can see all users)
const allUsers = await db.endpoints.data.query(
  'users',
  {
    select: ['id', 'username', 'email', 'role'],
    orderBy: [{ column: 'created_at', direction: 'desc' }],
  },
  { userId: 1, role: 'admin' }, // Admin context
);

console.log('All users (admin view):', allUsers);

// Query as user (can only see own data)
const userView = await db.endpoints.data.query(
  'users',
  {
    select: ['id', 'username', 'email', 'role'],
  },
  { userId: 2, role: 'user' }, // User context
);

console.log('User view (filtered by RLS):', userView);
```

### Update Data

Update a user's information:

```ts [ts]
// Update as admin
await db.endpoints.data.update(
  {
    tableName: 'users',
    id: 2,
    data: {
      role: 'moderator',
    },
  },
  { userId: 1, role: 'admin' }, // Admin context
);

// Update as user (can only update own data)
await db.endpoints.data.update(
  {
    tableName: 'users',
    id: 2,
    data: {
      username: 'newusername',
    },
  },
  { userId: 2, role: 'user' }, // User context
);
```

### Delete Data

Delete a user:

```ts [ts]
// Delete as admin
await db.endpoints.data.delete(
  {
    tableName: 'users',
    id: 2,
  },
  { userId: 1, role: 'admin' }, // Admin context
);
```

## Complete Example

Here's a complete example that puts everything together:

```ts [main.ts]
import { ForgeDatabase } from '@the-forgebase/database';
import knex from 'knex';

async function main() {
  // Create a Knex instance
  const knexInstance = knex({
    client: 'sqlite3',
    connection: {
      filename: './mydb.sqlite',
    },
    useNullAsDefault: true,
  });

  // Initialize ForgeDatabase
  const db = new ForgeDatabase({
    db: knexInstance,
    enforceRls: true,
    realtime: false,
  });

  // Create users table
  await db.endpoints.schema.create({
    tableName: 'users',
    columns: [
      { name: 'id', type: 'increments', primary: true },
      { name: 'username', type: 'string', unique: true, nullable: false },
      { name: 'email', type: 'string', unique: true, nullable: false },
      { name: 'password', type: 'string', nullable: false },
      { name: 'role', type: 'string', defaultValue: 'user' },
      { name: 'created_at', type: 'timestamp', defaultToNow: true },
    ],
  });

  // Set permissions
  await db.setPermissions('users', {
    operations: {
      SELECT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
        {
          allow: 'auth',
          fieldCheck: {
            field: 'id',
            operator: '===',
            valueType: 'userContext',
            value: 'userId',
          },
        },
      ],
      INSERT: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
      UPDATE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
        {
          allow: 'auth',
          fieldCheck: {
            field: 'id',
            operator: '===',
            valueType: 'userContext',
            value: 'userId',
          },
        },
      ],
      DELETE: [
        {
          allow: 'role',
          roles: ['admin'],
        },
      ],
    },
  });

  // Insert data
  const admin = await db.endpoints.data.create(
    {
      tableName: 'users',
      data: {
        username: 'admin',
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'admin',
      },
    },
    { userId: 0, role: 'system' },
    true,
  );

  const user = await db.endpoints.data.create(
    {
      tableName: 'users',
      data: {
        username: 'user1',
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'user',
      },
    },
    { userId: admin.id, role: 'admin' },
  );

  // Query data
  const allUsers = await db.endpoints.data.query(
    'users',
    {
      select: ['id', 'username', 'email', 'role'],
    },
    { userId: admin.id, role: 'admin' },
  );

  console.log('All users (admin view):', allUsers);

  const userView = await db.endpoints.data.query(
    'users',
    {
      select: ['id', 'username', 'email', 'role'],
    },
    { userId: user.id, role: 'user' },
  );

  console.log('User view (filtered by RLS):', userView);

  // Clean up
  await knexInstance.destroy();
}

main().catch(console.error);
```

## Next Steps

Now that you've set up the ForgeBase Database package and learned the basics, you can explore more advanced features:

- [Schema Management](/database/schema-management): Learn more about managing your database schema
- [Data Operations](/database/data-operations): Dive deeper into data operations
- [Row-Level Security](/database/row-level-security): Explore advanced RLS scenarios
- [Permissions](/database/permissions): Learn more about the permission system
- [Real-time Updates](/database/real-time): Enable real-time database updates
