---
title: Packages
description: Overview of the ForgeBase packages and their functionality
icon: 'lucide:package'
---

ForgeBase is organized into several packages, each providing specific functionality to the overall system. These packages can be used independently or together to build powerful backend services.

## Core Packages

### Database

The [`@the-forgebase/database`](/database/introduction) package provides a flexible, powerful database abstraction layer with support for multiple database engines, row-level security, and real-time capabilities.

### API

The [`@the-forgebase/api`](/api/introduction) package offers a framework-agnostic API layer that integrates with various Node.js frameworks and provides unified request handling, database operations, and more.

### Auth

The [`@the-forgebase/auth`](/auth/introduction) package delivers comprehensive authentication and authorization functionality with multiple strategies and session management.

### Storage

The [`@the-forgebase/storage`](/storage/introduction) package handles file storage with support for local and cloud storage providers.

### SDK

The [`@the-forgebase/sdk`](/sdk/introduction) package provides client-side libraries for interacting with ForgeBase services from web and mobile applications.

## Client Packages

### Web Auth

The [`@the-forgebase/web-auth`](/web-auth/introduction) package offers authentication utilities specifically for web applications.

### React Native Auth

The [`@the-forgebase/react-native-auth`](/react-native-auth/introduction) package provides authentication utilities for React Native applications.

## Utility Packages

### Common

The [`@the-forgebase/common`](/common/introduction) package contains shared utilities and types used across the ForgeBase ecosystem.

## Getting Started

To get started with ForgeBase packages, choose the package that best fits your needs and follow the installation and usage instructions in its documentation.

For a complete backend solution, we recommend using the following packages together:

:pm-install{name="@the-forgebase/api @the-forgebase/auth @the-forgebase/database @the-forgebase/storage"}

For js/ts client applications:

::tabs{variant="line"}
::div{label="Database"}
:pm-install{name="@the-forgebase/sdk"}
::

::div{label="Auth(web)"}
:pm-install{name="@the-forgebase/web-auth"}
::

::div{label="Auth(React Native)"}
:pm-install{name="@the-forgebase/react-native-auth"}
::

::
