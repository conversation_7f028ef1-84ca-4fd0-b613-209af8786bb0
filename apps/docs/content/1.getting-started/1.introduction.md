---
title: Introduction
description: ForgeBase is an open-source Backend as a Service (BaaS) framework designed to provide backend functionality for a variety of backend frameworks across multiple languages.
icon: 'lucide:door-open'

navTruncate: false
navBadges:
  - value: New
    type: lime

badges:
  - value: 0.1.0-alpha.1
  - value: Source
    icon: lucide:code
    to: https://github.com/The-ForgeBase/forgebase-ts
    target: _blank

authors:
  - name: <PERSON><PERSON><PERSON><PERSON>m
    username: SOG-web
    avatar: https://www.github.com/SOG-web.png
    to: https://github.com/SOG-web
    target: _blank
---

## Purpose

ForgeBase is an open-source Backend as a Service (BaaS) framework designed to provide backend functionality for a variety of backend frameworks across multiple languages. This TypeScript implementation uses [TurboRepo](/) as a monorepo management tool to organize the codebase into multiple libraries and applications.

## Core Features

- **Authentication & Authorization**: Fine-grained role, table, and namespace-level permissions
- **Database Integration**: Compatibility with modern real-time databases like RethinkDB, SurrealDB, etc.
- **Object Storage**: Built-in support for object storage solutions
- **Extendability**: Easy to add custom routes and extend functionality beyond the BaaS features
- **Real-time Features**: Full real-time support for database, presence, etc.

## Project Structure

ForgeBase is organized into two main directories:

- `apps/`: Contains the main applications for the project
- `packages/`: Contains the main libraries for the project

### Apps

- `apps/studio/`: The main application for the project
- `apps/docs/`: The documentation for the project
- `apps/nest-test/`: A NestJS integration example
- `apps/hono-test/`: A Hono integration example
- `apps/web-app/`: An example web application

### Libraries

- `packages/auth/`: A flexible authentication library providing multiple authentication strategies and framework adapters
- `packages/api/`: Provides API functionalities and integrations
- `packages/common/`: Common utilities and shared code used across the project
- `packages/database/`: Database management and integration
- `packages/real-time/`: Real-time communication and updates
- `packages/storage/`: Storage management and integration
- `packages/studio-ui-utils/`: UI utilities for the Studio application
- `packages/studio-ui/`: UI components for the Studio application
- `packages/sdk/`: SDK for interacting with ForgeBase
- `packages/react-native-auth/`: Authentication library for React Native
- `packages/web-auth/`: Authentication library for web applications

## Getting Started

### Prerequisites

- Node.js (LTS version recommended)
- pnpm

### Installation

1. Clone the repository:

   ```bash [git]
   git clone https://github.com/The-ForgeBase/forgebase-ts.git
   cd forgebase-ts
   ```

2. Install dependencies:
   :pm-install

### Build

To build all apps and packages, run:

:pm-run{script="build"}

### Develop

To develop all apps and packages, run:

:pm-run{script="dev"}

## Motivation

Our mission is to simplify backend development by providing a highly flexible, language-agnostic BaaS framework that developers can plug into their existing server setup. While we are 70% inspired by Pocketbase, we recognized its limitations—particularly its dependency on SQLite and its inability to scale horizontally. To overcome these challenges, we are building a better alternative that supports horizontal scaling and integrates with more robust databases like PostgreSQL, SurrealDB, etc.

<!-- - [ePoc](https://epoc.inria.fr/en)
- [Add your project 🚀](https://github.com/The-ForgeBase/forgebase-ts/edit/main/README.md) -->

## License

[MIT](https://github.com/The-ForgeBase/forgebase-ts/blob/main/LICENSE)
