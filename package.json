{"name": "the-forgebase", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "changeset:autogenerate": "node .changeset/changesets-autogenerate.mjs", "release": "pnpm changeset version && pnpm changeset publish"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.4", "fast-glob": "^3.3.3", "prettier": "^3.5.3", "turbo": "^2.5.3", "typescript": "5.8.2", "unbuild": "^3.5.0"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}