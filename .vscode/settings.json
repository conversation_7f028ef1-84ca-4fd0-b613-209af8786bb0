{
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  // Required for the extension
  "mdc.enableFormatting": true,
  // Recommended (for `mdc` and `md`, depending on your usage)
  "[mdc]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.formatOnPaste": true
  },
  // allow autocomplete for ArkType expressions like "string | num"
  "editor.quickSuggestions": {
    "strings": "on"
  },
  // prioritize ArkType's "type" for autoimports
  "typescript.preferences.autoImportSpecifierExcludeRegexes": ["^(node:)?os$"]
}
